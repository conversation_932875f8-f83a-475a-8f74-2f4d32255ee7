# 🔄 WitMall实体类共享使用指南

## 📋 概述

本指南说明如何在WitMall微服务架构中实现跨服务的实体类共享，确保数据一致性和代码复用。

## 🏗️ 共享实体类架构

### 1. 共享实体类位置
```
wit-common/
└── src/main/java/com/wit/common/entity/
    ├── UserBasicInfo.java      # 用户基础信息
    ├── ProductBasicInfo.java   # 商品基础信息
    ├── OrderBasicInfo.java     # 订单基础信息（待创建）
    └── MerchantBasicInfo.java  # 商家基础信息（待创建）
```

### 2. 设计原则

#### ✅ 应该共享的实体特征
- **轻量级**: 只包含最基本的字段
- **稳定性**: 字段变化频率低
- **通用性**: 多个服务都需要使用
- **脱敏性**: 敏感信息已脱敏处理

#### ❌ 不应该共享的实体特征
- **业务专用**: 只有特定服务使用
- **复杂关联**: 包含复杂的关联关系
- **频繁变更**: 业务逻辑经常变化
- **敏感信息**: 包含未脱敏的敏感数据

## 🎯 使用场景

### 1. UserBasicInfo 使用场景

#### 在订单服务中使用
```java
@Entity
@Table(name = "order_info")
public class OrderInfo extends BaseEntity {
    
    // 订单基本信息
    private String orderNo;
    private BigDecimal totalAmount;
    
    // 用户基础信息（共享实体）
    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "userId", column = @Column(name = "user_id")),
        @AttributeOverride(name = "username", column = @Column(name = "username")),
        @AttributeOverride(name = "nickname", column = @Column(name = "user_nickname"))
    })
    private UserBasicInfo userInfo;
    
    // 其他订单字段...
}
```

#### 在购物车服务中使用
```java
@Entity
@Table(name = "cart_item")
public class CartItem extends BaseEntity {
    
    // 购物车基本信息
    private Integer quantity;
    private BigDecimal price;
    
    // 用户基础信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "user_id")
    private UserBasicInfo userInfo;
    
    // 商品基础信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", referencedColumnName = "product_id")
    private ProductBasicInfo productInfo;
}
```

### 2. ProductBasicInfo 使用场景

#### 在库存服务中使用
```java
@Entity
@Table(name = "inventory_info")
public class InventoryInfo extends BaseEntity {
    
    // 库存基本信息
    private Integer availableStock;
    private Integer lockedStock;
    private Integer totalStock;
    
    // 商品基础信息（共享实体）
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", referencedColumnName = "product_id")
    private ProductBasicInfo productInfo;
    
    // 仓库信息
    private Long warehouseId;
    private String warehouseName;
}
```

#### 在搜索服务中使用
```java
@Document(indexName = "product_search")
public class ProductSearchIndex {
    
    @Id
    private String id;
    
    // 商品基础信息（用于搜索）
    @Field(type = FieldType.Object)
    private ProductBasicInfo productInfo;
    
    // 搜索相关字段
    @Field(type = FieldType.Text, analyzer = "ik_max_word")
    private String searchKeywords;
    
    @Field(type = FieldType.Integer)
    private Integer salesCount;
    
    @Field(type = FieldType.Double)
    private Double rating;
}
```

## 🔧 实现方式

### 1. Maven依赖配置

#### 在需要使用共享实体的服务中添加依赖
```xml
<!-- 在 wit-order/pom.xml 中 -->
<dependencies>
    <!-- 引入Common模块 -->
    <dependency>
        <groupId>com.wit</groupId>
        <artifactId>wit-common</artifactId>
        <version>1.0.0</version>
    </dependency>
    
    <!-- 其他依赖... -->
</dependencies>
```

### 2. JPA实体映射

#### 方式一：嵌入式映射（推荐）
```java
@Entity
@Table(name = "order_info")
public class OrderInfo extends BaseEntity {
    
    @Embedded
    @AttributeOverrides({
        @AttributeOverride(name = "userId", column = @Column(name = "order_user_id")),
        @AttributeOverride(name = "username", column = @Column(name = "order_username")),
        @AttributeOverride(name = "nickname", column = @Column(name = "order_user_nickname"))
    })
    private UserBasicInfo orderUser;
}
```

#### 方式二：关联映射
```java
@Entity
@Table(name = "cart_item")
public class CartItem extends BaseEntity {
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private UserBasicInfo userInfo;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id")
    private ProductBasicInfo productInfo;
}
```

### 3. 数据同步策略

#### 策略一：事件驱动同步（推荐）
```java
// 在用户服务中，当用户信息更新时发送事件
@Service
public class UserService {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    public void updateUser(User user) {
        // 更新用户信息
        userRepository.save(user);
        
        // 发送用户信息更新事件
        UserBasicInfo userBasicInfo = UserBasicInfo.create(
            user.getId(), user.getUsername(), user.getNickname(),
            user.getAvatar(), user.getPhone(), user.getEmail(),
            user.getUserType(), user.getUserLevel(), user.getVipLevel(),
            user.getIsVip(), user.getStatus(), user.getTenantId()
        );
        
        eventPublisher.publishEvent(new UserInfoUpdatedEvent(userBasicInfo));
    }
}

// 在其他服务中监听事件并更新本地数据
@EventListener
public void handleUserInfoUpdated(UserInfoUpdatedEvent event) {
    UserBasicInfo userInfo = event.getUserInfo();
    // 更新本地的用户基础信息
    userBasicInfoRepository.save(userInfo);
}
```

#### 策略二：定时同步
```java
@Component
public class UserInfoSyncTask {
    
    @Scheduled(fixedRate = 300000) // 每5分钟同步一次
    public void syncUserBasicInfo() {
        // 从用户服务获取更新的用户信息
        List<UserBasicInfo> updatedUsers = userServiceClient.getUpdatedUsers();
        
        // 批量更新本地数据
        userBasicInfoRepository.saveAll(updatedUsers);
    }
}
```

## 📊 数据库设计

### 1. 共享实体表结构

#### user_basic_info 表
```sql
CREATE TABLE `user_basic_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `nickname` VARCHAR(50) COMMENT '昵称',
    `avatar` VARCHAR(255) COMMENT '头像URL',
    `phone` VARCHAR(20) COMMENT '手机号（脱敏）',
    `email` VARCHAR(100) COMMENT '邮箱（脱敏）',
    `user_type` TINYINT DEFAULT 1 COMMENT '用户类型',
    `user_level` VARCHAR(20) COMMENT '用户等级',
    `vip_level` VARCHAR(20) COMMENT 'VIP等级',
    `is_vip` TINYINT DEFAULT 0 COMMENT '是否VIP',
    `status` TINYINT DEFAULT 1 COMMENT '状态',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除',
    `version` INT DEFAULT 1 COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    KEY `idx_username` (`username`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';
```

#### product_basic_info 表
```sql
CREATE TABLE `product_basic_info` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '商品ID',
    `product_code` VARCHAR(50) NOT NULL COMMENT '商品编码',
    `product_name` VARCHAR(200) NOT NULL COMMENT '商品名称',
    `product_subtitle` VARCHAR(500) COMMENT '商品副标题',
    `main_image` VARCHAR(255) COMMENT '商品主图',
    `category_id` BIGINT COMMENT '分类ID',
    `category_name` VARCHAR(100) COMMENT '分类名称',
    `brand_id` BIGINT COMMENT '品牌ID',
    `brand_name` VARCHAR(100) COMMENT '品牌名称',
    `price` DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    `original_price` DECIMAL(10,2) COMMENT '原价',
    `cost_price` DECIMAL(10,2) COMMENT '成本价',
    `weight` INT COMMENT '重量（克）',
    `unit` VARCHAR(20) COMMENT '单位',
    `status` TINYINT DEFAULT 1 COMMENT '状态',
    `stock` INT DEFAULT 0 COMMENT '库存',
    `merchant_id` BIGINT COMMENT '商家ID',
    `merchant_name` VARCHAR(100) COMMENT '商家名称',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_by` BIGINT COMMENT '创建人',
    `update_by` BIGINT COMMENT '更新人',
    `deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除',
    `version` INT DEFAULT 1 COMMENT '版本号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_product_id` (`product_id`),
    UNIQUE KEY `uk_product_code` (`product_code`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_brand_id` (`brand_id`),
    KEY `idx_merchant_id` (`merchant_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品基础信息表';
```

## 🚀 最佳实践

### 1. 版本控制
- 共享实体类的变更需要谨慎处理
- 使用版本号控制兼容性
- 新增字段使用默认值，避免破坏性变更

### 2. 性能优化
- 使用懒加载避免不必要的数据查询
- 合理使用缓存减少数据库访问
- 批量操作提高数据同步效率

### 3. 数据一致性
- 使用事件驱动确保数据及时同步
- 实现幂等性处理重复事件
- 定期校验数据一致性

### 4. 监控告警
- 监控数据同步延迟
- 设置数据不一致告警
- 记录同步失败日志

## 📝 注意事项

1. **避免循环依赖**: 共享实体应该是单向依赖
2. **控制实体大小**: 保持共享实体轻量级
3. **敏感信息处理**: 确保敏感信息已脱敏
4. **版本兼容性**: 变更时考虑向后兼容
5. **性能影响**: 评估共享实体对性能的影响

## 🔗 相关文档

- [数据库设计文档](DATABASE_DESIGN.md)
- [微服务架构指南](PROJECT_OVERVIEW.md)
- [开发规范指南](GETTING_STARTED.md)
