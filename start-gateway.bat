@echo off
setlocal enabledelayedexpansion

echo ========================================
echo   Wit Mall Gateway - Spring Boot 3
echo   现代化微服务网关启动脚本
echo ========================================

echo.
echo 🚀 Spring Boot 3 现代化特性:
echo    ✅ EnvironmentPostProcessor 早期配置注入
echo    ✅ 响应式Redis配置支持
echo    ✅ 多租户路由和限流
echo    ✅ 高性能HTTP客户端

echo.
echo 🏗️ 编译项目...
cd /d "%~dp0"
call mvn clean compile -DskipTests -q
if %errorlevel% neq 0 (
    echo    ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo 🚀 启动网关服务...

REM Spring Boot 3 JVM优化参数
set JAVA_OPTS=-Dspring.profiles.active=local
set JAVA_OPTS=%JAVA_OPTS% -Xms512m -Xmx1024m
set JAVA_OPTS=%JAVA_OPTS% -XX:+UseG1GC
set JAVA_OPTS=%JAVA_OPTS% -XX:MaxGCPauseMillis=200

echo    启动中...
echo.

java %JAVA_OPTS% -jar wit-gateway/target/wit-gateway-1.0.0.jar

pause
