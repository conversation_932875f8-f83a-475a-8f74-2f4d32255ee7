# Spring Boot 3 现代化配置 - 使用 Config Data API
# 这是学习Spring Boot 3最新特性的最佳实践

# 基本日志配置（详细配置在 Nacos 中）
logging:
  level:
    root: INFO
    com.wit: DEBUG
    org.springframework.cloud.gateway: INFO
    org.springframework.cloud.nacos: WARN
    org.springframework.core.env: WARN
    org.springframework.data.redis: WARN
    org.springframework.boot.autoconfigure.data.redis: WARN
    io.lettuce.core: WARN
    reactor.netty: WARN
    io.netty: WARN
    # 关闭JMX和RMI的DEBUG日志
    sun.rmi: WARN
    javax.management: WARN
    com.alibaba.nacos.shaded.io.grpc.netty: WARN
    com.alibaba.nacos.client: WARN

# 监控配置（详细配置在 Nacos 中，这里只保留基本配置）

# 本地性能配置（基础配置，详细配置在 Nacos 中）

# Spring Boot 3 Config Data API 配置
spring:
  application:
    name: wit-gateway
  profiles:
    active: local

  # 🚀 Spring Boot 3 现代化配置
  # 注意：实际的Nacos配置通过EarlyNacosConfig（EnvironmentPostProcessor）在启动早期注入
  # 这是Spring Boot 3推荐的现代化配置方式，不依赖bootstrap.yml

  cloud:
    # Nacos配置 - Spring Boot 3 现代化方式
    nacos:
      discovery:
        server-addr: ***************:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: DEFAULT_GROUP
        cluster-name: wit-cluster
        username: nacos
        password: nacos
        # 强制指定服务器地址
        ip: *************
        port: 8080
        metadata:
          version: 1.0.0
          zone: prod
          service-type: gateway
      config:
        server-addr: ***************:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: DEFAULT_GROUP
        file-extension: yml
        username: nacos
        password: nacos
        refresh-enabled: true
        # 强制指定服务器地址，避免使用localhost
        remote-first: true
        # 共享配置
        shared-configs:
          - data-id: common-redis.yml
            group: COMMON_GROUP
            refresh: true

    # Spring Cloud Gateway 网关配置
    gateway:
      # ===================================================================
      # 全局CORS跨域配置 - 允许前端跨域访问
      # ===================================================================
      globalcors:
        cors-configurations:
          '[/**]':                                  # 匹配所有路径
            allowed-origins: "*"                    # 允许所有来源（生产环境建议指定具体域名）
            allowed-methods: "*"                    # 允许所有HTTP方法
            allowed-headers: "*"                    # 允许所有请求头
            allow-credentials: false                # 不允许携带凭证（与allowed-origins: "*"冲突时设为false）
            max-age: 3600                          # 预检请求缓存时间（秒）

      # ===================================================================
      # HTTP客户端配置 - 优化网关性能
      # ===================================================================
      httpclient:
        connect-timeout: 10000                      # 连接超时时间（毫秒）
        response-timeout: 30s                       # 响应超时时间
        # 连接池配置
        pool:
          type: elastic                             # 弹性连接池
          max-connections: 1000                     # 最大连接数
          max-idle-time: 30s                       # 最大空闲时间
          max-life-time: 10m                       # 连接最大生存时间
          acquire-timeout: 60                       # 获取连接超时时间（秒）

      # ===================================================================
      # 基础路由配置 - 健康检查和监控端点
      # ===================================================================
      routes:
        # 健康检查路由 - 用于负载均衡器健康检查
        - id: health-check
          uri: http://localhost:8080
          predicates:
            - Path=/health
          filters:
            - SetStatus=200                         # 直接返回200状态码

        # Actuator监控路由 - 应用监控端点
        - id: actuator-route
          uri: http://localhost:8080
          predicates:
            - Path=/actuator/**

  # ===================================================================
  # Spring Boot 应用配置
  # ===================================================================
  main:
    allow-bean-definition-overriding: true         # 允许Bean定义覆盖（解决Redis配置冲突）
    allow-circular-references: true                # 允许循环引用
    web-application-type: reactive                 # 明确指定为响应式应用（WebFlux）

  # ===================================================================
  # 自动配置排除 - 网关服务不需要数据库相关配置
  # ===================================================================
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# ===================================================================
# 自定义配置 - 禁用不需要的组件
# ===================================================================
wit:
  web:
    enabled: false                                  # 禁用WebConfig（网关使用响应式架构，不需要Servlet拦截器）


