# Spring Boot 3 现代化配置 - 使用 Config Data API
# 这是学习Spring Boot 3最新特性的最佳实践

# 基本日志配置（详细配置在 Nacos 中）
logging:
  level:
    root: INFO
    com.wit: DEBUG
    org.springframework.cloud.gateway: INFO
    org.springframework.cloud.nacos: WARN
    org.springframework.core.env: WARN
    org.springframework.data.redis: WARN
    org.springframework.boot.autoconfigure.data.redis: WARN
    io.lettuce.core: WARN
    reactor.netty: WARN
    io.netty: WARN
    # 关闭JMX和RMI的DEBUG日志
    sun.rmi: WARN
    javax.management: WARN
    com.alibaba.nacos.shaded.io.grpc.netty: WARN
    com.alibaba.nacos.client: WARN

# 监控配置（详细配置在 Nacos 中，这里只保留基本配置）

# 本地性能配置（基础配置，详细配置在 Nacos 中）

# Spring Boot 3 Config Data API 配置
spring:
  application:
    name: wit-gateway
  profiles:
    active: local

  # 🚀 Spring Boot 3 现代化配置
  # 注意：实际的Nacos配置通过EarlyNacosConfig（EnvironmentPostProcessor）在启动早期注入
  # 这是Spring Boot 3推荐的现代化配置方式，不依赖bootstrap.yml

  cloud:
    # Nacos配置 - Spring Boot 3 现代化方式
    nacos:
      discovery:
        server-addr: ***************:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: DEFAULT_GROUP
        cluster-name: wit-cluster
        username: nacos
        password: nacos
        # 强制指定服务器地址
        ip: *************
        port: 8080
        metadata:
          version: 1.0.0
          zone: prod
          service-type: gateway
      config:
        server-addr: ***************:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: DEFAULT_GROUP
        file-extension: yml
        username: nacos
        password: nacos
        refresh-enabled: true
        # 强制指定服务器地址，避免使用localhost
        remote-first: true
        # 共享配置
        shared-configs:
          - data-id: common-redis.yml
            group: COMMON_GROUP
            refresh: true

    gateway:
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: false
            max-age: 3600
      # HTTP客户端配置
      httpclient:
        connect-timeout: 10000
        response-timeout: 30s
        pool:
          type: elastic
          max-connections: 1000
          max-idle-time: 30s
          max-life-time: 10m
          acquire-timeout: 60

      # 基本路由配置
      routes:
        # 健康检查路由
        - id: health-check
          uri: http://localhost:8080
          predicates:
            - Path=/health
          filters:
            - SetStatus=200

        # Actuator监控路由
        - id: actuator-route
          uri: http://localhost:8080
          predicates:
            - Path=/actuator/**



  # 允许Bean定义覆盖（解决Redis配置冲突）
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    web-application-type: reactive  # 明确指定为响应式应用

  # 禁用数据源自动配置（网关不需要数据库）
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# 禁用 WebConfig（网关使用响应式架构，不需要 Servlet 拦截器）
wit:
  web:
    enabled: false


