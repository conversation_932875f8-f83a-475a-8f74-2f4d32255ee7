# ===================================================================
# Wit Mall 网关服务配置文件
# ===================================================================
#
# 项目名称：Wit Mall 微服务商城
# 服务名称：API网关服务 (wit-gateway)
# 服务端口：8080
# 服务描述：统一API入口，负责路由转发、认证授权、限流熔断等功能
#
# 技术栈：
# - Spring Boot 3.2.1
# - Spring Cloud Gateway
# - Spring Cloud Alibaba
# - Nacos (服务注册发现 + 配置管理)
# - Redis (缓存 + 限流)
#
# 配置说明：
# 1. 使用Spring Boot 3现代化配置方式，无需bootstrap.yml
# 2. Nacos配置直接在application.yml中配置，简化部署
# 3. 支持多环境配置（local/dev/test/prod）
# 4. 集成Redis用于限流和缓存
# 5. 响应式架构，排除传统Servlet组件
# ===================================================================

# ===================================================================
# 日志配置 - 开发环境详细日志，生产环境精简日志
# ===================================================================
logging:
  level:
    root: INFO
    com.wit: DEBUG                                    # 项目包日志级别
    org.springframework.cloud.gateway: INFO          # 网关日志
    org.springframework.cloud.nacos: WARN           # Nacos客户端日志
    org.springframework.core.env: WARN              # 环境配置日志
    org.springframework.data.redis: WARN            # Redis日志
    org.springframework.boot.autoconfigure.data.redis: WARN
    io.lettuce.core: WARN                           # Redis客户端日志
    reactor.netty: WARN                             # Netty日志
    io.netty: WARN                                  # Netty底层日志
    # 关闭JMX和RMI的DEBUG日志，减少日志噪音
    sun.rmi: WARN
    javax.management: WARN
    com.alibaba.nacos.shaded.io.grpc.netty: WARN
    com.alibaba.nacos.client: WARN
  pattern:
    # 控制台日志格式：时间 [线程] 级别 [类名] - 消息
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n'

# ===================================================================
# Spring Boot 核心配置
# ===================================================================
spring:
  application:
    name: wit-gateway                               # 服务名称，用于服务注册发现
  profiles:
    active: local                                   # 激活的配置文件（local/dev/test/prod）

  # ===================================================================
  # Spring Cloud 微服务配置
  # ===================================================================
  cloud:
    # Nacos 服务注册发现配置
    nacos:
      discovery:
        server-addr: localhost:8848                 # Nacos服务器地址（修改为localhost）
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # 命名空间ID（wit环境）
        group: DEFAULT_GROUP                        # 服务分组
        cluster-name: wit-cluster                   # 集群名称
        username: nacos                             # Nacos用户名
        password: nacos                             # Nacos密码
        # 连接配置优化
        heart-beat-interval: 5000                   # 心跳间隔（毫秒）
        heart-beat-timeout: 15000                   # 心跳超时（毫秒）
        ip-delete-timeout: 30000                    # IP删除超时（毫秒）
        # 服务实例配置
        ip: 127.0.0.1                              # 服务IP地址（本地开发环境）
        port: 8080                                  # 服务端口
        # 服务元数据
        metadata:
          version: 1.0.0                           # 服务版本
          zone: local                              # 部署区域（本地环境）
          service-type: gateway                    # 服务类型标识

      # Nacos 配置管理
      config:
        server-addr: localhost:8848                 # 配置中心地址（修改为localhost）
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # 配置命名空间
        group: DEFAULT_GROUP                        # 配置分组
        file-extension: yml                         # 配置文件扩展名
        username: nacos                             # 配置中心用户名
        password: nacos                             # 配置中心密码
        refresh-enabled: true                       # 启用配置自动刷新
        remote-first: false                         # 优先使用本地配置（避免启动时连接问题）
        # 连接配置优化
        timeout: 3000                               # 连接超时时间（毫秒）
        config-long-poll-timeout: 30000            # 长轮询超时时间（毫秒）
        config-retry-time: 2000                     # 重试间隔时间（毫秒）
        max-retry: 3                                # 最大重试次数
        enable-remote-sync-config: true             # 启用远程同步配置
        # 共享配置文件（暂时注释，避免启动时连接问题）
        # shared-configs:
        #   - data-id: common-redis.yml               # 公共Redis配置
        #     group: COMMON_GROUP                     # 公共配置分组
        #     refresh: true                           # 支持动态刷新

    # Spring Cloud Gateway 网关配置
    gateway:
      # ===================================================================
      # 全局CORS跨域配置 - 允许前端跨域访问
      # ===================================================================
      globalcors:
        cors-configurations:
          '[/**]':                                  # 匹配所有路径
            allowed-origins: "*"                    # 允许所有来源（生产环境建议指定具体域名）
            allowed-methods: "*"                    # 允许所有HTTP方法
            allowed-headers: "*"                    # 允许所有请求头
            allow-credentials: false                # 不允许携带凭证（与allowed-origins: "*"冲突时设为false）
            max-age: 3600                          # 预检请求缓存时间（秒）

      # ===================================================================
      # HTTP客户端配置 - 优化网关性能
      # ===================================================================
      httpclient:
        connect-timeout: 10000                      # 连接超时时间（毫秒）
        response-timeout: 30s                       # 响应超时时间
        # 连接池配置
        pool:
          type: elastic                             # 弹性连接池
          max-connections: 1000                     # 最大连接数
          max-idle-time: 30s                       # 最大空闲时间
          max-life-time: 10m                       # 连接最大生存时间
          acquire-timeout: 60                       # 获取连接超时时间（秒）

      # ===================================================================
      # 基础路由配置 - 健康检查和监控端点
      # ===================================================================
      routes:
        # 健康检查路由 - 用于负载均衡器健康检查
        - id: health-check
          uri: http://localhost:8080
          predicates:
            - Path=/health
          filters:
            - SetStatus=200                         # 直接返回200状态码

        # Actuator监控路由 - 应用监控端点
        - id: actuator-route
          uri: http://localhost:8080
          predicates:
            - Path=/actuator/**

  # ===================================================================
  # Spring Boot 应用配置
  # ===================================================================
  main:
    allow-bean-definition-overriding: true         # 允许Bean定义覆盖（解决Redis配置冲突）
    allow-circular-references: true                # 允许循环引用
    web-application-type: reactive                 # 明确指定为响应式应用（WebFlux）

  # ===================================================================
  # 自动配置排除 - 网关服务不需要数据库相关配置
  # ===================================================================
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# ===================================================================
# 自定义配置 - 禁用不需要的组件
# ===================================================================
wit:
  web:
    enabled: false                                  # 禁用WebConfig（网关使用响应式架构，不需要Servlet拦截器）


