package com.wit.gateway;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Wit Mall 网关服务启动类
 *
 * ===================================================================
 * 服务功能：
 * 1. API网关 - 统一入口，路由转发
 * 2. 认证授权 - JWT token验证，权限控制
 * 3. 限流熔断 - 保护后端服务，防止过载
 * 4. 监控日志 - 请求追踪，性能监控
 * 5. 跨域处理 - 支持前端跨域访问
 *
 * 技术特性：
 * - 基于Spring Cloud Gateway响应式架构
 * - 集成Nacos服务注册发现和配置管理
 * - 支持多租户路由（/{tenantId}/{service}/**）
 * - Redis缓存和限流支持
 * - 完整的过滤器链（认证->限流->监控->熔断）
 *
 * 部署信息：
 * - 服务端口：8080
 * - 健康检查：/health
 * - 监控端点：/actuator/**
 * - 网关管理：/gateway/**
 * ===================================================================
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@SpringBootApplication(
    // 排除不需要的自动配置类（网关服务不需要Web MVC和数据库）
    exclude = {
        WebMvcAutoConfiguration.class,              // 排除传统Web MVC（使用WebFlux响应式）
        DataSourceAutoConfiguration.class,         // 排除数据源自动配置
        HibernateJpaAutoConfiguration.class        // 排除JPA自动配置
    },
    // 排除MyBatis Plus自动配置（网关不需要数据库操作）
    excludeName = {
        "com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration"
    }
)
@EnableDiscoveryClient                              // 启用服务注册发现
@ComponentScan(
    // 扫描网关和公共模块的组件
    basePackages = {"com.wit.gateway", "com.wit.common"},
    // 排除不需要的配置类
    excludeFilters = {
        @ComponentScan.Filter(
            type = FilterType.REGEX,
            pattern = "com\\.wit\\.common\\.config\\.MybatisPlusConfig.*"
        )
    }
)
public class WitGatewayApplication {

    /**
     * 网关服务启动入口
     *
     * @param args 启动参数
     */
    public static void main(String[] args) {
        // 创建SpringApplication实例并明确设置为响应式应用
        SpringApplication app = new SpringApplication(WitGatewayApplication.class);
        app.setWebApplicationType(WebApplicationType.REACTIVE);  // 强制使用WebFlux响应式架构

        // 启动应用并获取应用上下文
        ConfigurableApplicationContext context = app.run(args);
        Environment env = context.getEnvironment();

        // 打印启动信息
        logApplicationStartup(env);
    }

    /**
     * 打印应用启动信息
     * 包括访问地址、管理端点、多租户路由格式等关键信息
     *
     * @param env Spring环境对象
     */
    private static void logApplicationStartup(Environment env) {
        // 确定协议类型（HTTP或HTTPS）
        String protocol = "http";
        if (env.getProperty("server.ssl.key-store") != null) {
            protocol = "https";
        }

        // 获取服务端口和上下文路径
        String serverPort = env.getProperty("server.port", "8080");
        String contextPath = env.getProperty("server.servlet.context-path", "");

        // 获取主机地址
        String hostAddress = "localhost";
        try {
            hostAddress = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("无法确定主机地址，使用localhost作为备用", e);
        }

        log.info("\n----------------------------------------------------------\n" +
                "🚀 Wit Mall Gateway 启动成功！\n" +
                "----------------------------------------------------------\n" +
                "🌐 应用访问地址:\n" +
                "   本地地址:    {}://localhost:{}{}\n" +
                "   外部地址:    {}://{}:{}{}\n" +
                "📊 管理端点:\n" +
                "   健康检查:    {}://localhost:{}/gateway/health\n" +
                "   网关信息:    {}://localhost:{}/gateway/info\n" +
                "   租户管理:    {}://localhost:{}/gateway/tenants\n" +
                "   性能指标:    {}://localhost:{}/gateway/metrics\n" +
                "🏢 多租户路由:\n" +
                "   路由格式:    /{tenantId}/{service}/**\n" +
                "   示例:        /3921/user/api/v1/profile\n" +
                "🔧 环境信息:\n" +
                "   配置文件:    {}\n" +
                "   JVM版本:     {}\n" +
                "----------------------------------------------------------",
                protocol, serverPort, contextPath,
                protocol, hostAddress, serverPort, contextPath,
                protocol, serverPort,
                protocol, serverPort,
                protocol, serverPort,
                protocol, serverPort,
                env.getActiveProfiles().length == 0 ? "default" : String.join(", ", env.getActiveProfiles()),
                System.getProperty("java.version"));
    }
}
