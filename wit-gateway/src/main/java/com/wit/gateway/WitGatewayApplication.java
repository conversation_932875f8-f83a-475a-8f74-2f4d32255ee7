package com.wit.gateway;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 网关服务启动类
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(
    exclude = {
        WebMvcAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
    },
    excludeName = {
        "com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration"
    }
)
@EnableDiscoveryClient
@ComponentScan(
    basePackages = {"com.wit.gateway", "com.wit.common"},
    excludeFilters = {
        @ComponentScan.Filter(
            type = FilterType.REGEX,
            pattern = "com\\.wit\\.common\\.config\\.MybatisPlusConfig.*"
        )
    }
)
public class WitGatewayApplication {

    public static void main(String[] args) {
        // 创建 SpringApplication 实例并明确设置为响应式应用
        SpringApplication app = new SpringApplication(WitGatewayApplication.class);
        app.setWebApplicationType(WebApplicationType.REACTIVE);

        ConfigurableApplicationContext context = app.run(args);
        Environment env = context.getEnvironment();

        logApplicationStartup(env);
    }

    private static void logApplicationStartup(Environment env) {
        String protocol = "http";
        if (env.getProperty("server.ssl.key-store") != null) {
            protocol = "https";
        }

        String serverPort = env.getProperty("server.port", "8080");
        String contextPath = env.getProperty("server.servlet.context-path", "");
        String hostAddress = "localhost";

        try {
            hostAddress = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("无法确定主机地址", e);
        }

        log.info("\n----------------------------------------------------------\n" +
                "🚀 Wit Mall Gateway 启动成功！\n" +
                "----------------------------------------------------------\n" +
                "🌐 应用访问地址:\n" +
                "   本地地址:    {}://localhost:{}{}\n" +
                "   外部地址:    {}://{}:{}{}\n" +
                "📊 管理端点:\n" +
                "   健康检查:    {}://localhost:{}/gateway/health\n" +
                "   网关信息:    {}://localhost:{}/gateway/info\n" +
                "   租户管理:    {}://localhost:{}/gateway/tenants\n" +
                "   性能指标:    {}://localhost:{}/gateway/metrics\n" +
                "🏢 多租户路由:\n" +
                "   路由格式:    /{tenantId}/{service}/**\n" +
                "   示例:        /3921/user/api/v1/profile\n" +
                "🔧 环境信息:\n" +
                "   配置文件:    {}\n" +
                "   JVM版本:     {}\n" +
                "----------------------------------------------------------",
                protocol, serverPort, contextPath,
                protocol, hostAddress, serverPort, contextPath,
                protocol, serverPort,
                protocol, serverPort,
                protocol, serverPort,
                protocol, serverPort,
                env.getActiveProfiles().length == 0 ? "default" : String.join(", ", env.getActiveProfiles()),
                System.getProperty("java.version"));
    }
}
