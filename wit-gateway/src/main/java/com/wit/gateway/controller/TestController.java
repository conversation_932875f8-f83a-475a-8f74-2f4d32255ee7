package com.wit.gateway.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于测试Redis连接和网关功能
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Mono<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        result.put("service", "wit-gateway");
        
        log.info("健康检查请求");
        return Mono.just(result);
    }

    /**
     * Redis连接测试
     */
    @GetMapping("/redis")
    public Mono<Map<String, Object>> testRedis() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查连接工厂信息
            if (redisConnectionFactory instanceof LettuceConnectionFactory) {
                LettuceConnectionFactory factory = (LettuceConnectionFactory) redisConnectionFactory;
                result.put("redis_host", factory.getHostName());
                result.put("redis_port", factory.getPort());
                result.put("redis_database", factory.getDatabase());
            }
            
            // 测试Redis连接
            String testKey = "test:gateway:" + System.currentTimeMillis();
            String testValue = "Hello Redis from Gateway!";
            
            redisTemplate.opsForValue().set(testKey, testValue);
            String retrievedValue = (String) redisTemplate.opsForValue().get(testKey);
            
            result.put("status", "SUCCESS");
            result.put("test_key", testKey);
            result.put("test_value", testValue);
            result.put("retrieved_value", retrievedValue);
            result.put("connection_success", testValue.equals(retrievedValue));
            
            // 清理测试数据
            redisTemplate.delete(testKey);
            
            log.info("Redis连接测试成功: {}", result);
            
        } catch (Exception e) {
            result.put("status", "ERROR");
            result.put("error", e.getMessage());
            result.put("connection_success", false);
            
            log.error("Redis连接测试失败", e);
        }
        
        return Mono.just(result);
    }

    /**
     * 网关信息
     */
    @GetMapping("/info")
    public Mono<Map<String, Object>> info() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "wit-gateway");
        result.put("version", "1.0.0");
        result.put("spring_boot_version", "3.2.1");
        result.put("java_version", System.getProperty("java.version"));
        result.put("profiles", System.getProperty("spring.profiles.active", "default"));
        
        log.info("网关信息请求");
        return Mono.just(result);
    }
}
