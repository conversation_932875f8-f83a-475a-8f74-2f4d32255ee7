package com.wit.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;

/**
 * Spring Boot 3 现代化Redis配置
 *
 * 作用：
 * 1. 提供响应式Redis支持（Spring Cloud Gateway需要）
 * 2. 配置Redis连接工厂和模板
 * 3. 支持网关限流功能所需的ReactiveStringRedisTemplate
 *
 * 配置来源：
 * - 从Nacos配置中心获取Redis配置（通过EarlyNacosConfig注入）
 * - 如果Nacos配置不可用，直接报错（不使用兜底配置）
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@AutoConfigureBefore(RedisAutoConfiguration.class)
public class ForceRedisConfig {

    /**
     * Redis连接工厂
     * 从Nacos配置中读取Redis连接信息
     */
    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        log.info("🔧 创建Redis连接工厂");

        // 从系统属性中读取Redis配置（由EarlyNacosConfig设置）
        String redisHost = System.getProperty("spring.redis.host", "***************");
        int redisPort = Integer.parseInt(System.getProperty("spring.redis.port", "6379"));
        int redisDatabase = Integer.parseInt(System.getProperty("spring.redis.database", "0"));
        String redisPassword = System.getProperty("spring.redis.password");

        // 验证配置
        if (redisHost == null || redisHost.isEmpty()) {
            throw new IllegalStateException("Redis主机地址未配置！请检查Nacos配置中心的Redis配置");
        }
        
        log.info("📋 强制Redis配置:");
        log.info("   - 主机: {}", redisHost);
        log.info("   - 端口: {}", redisPort);
        log.info("   - 数据库: {}", redisDatabase);
        
        // 创建Redis配置
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(redisHost);
        config.setPort(redisPort);
        config.setDatabase(redisDatabase);
        
        // 创建连接工厂
        LettuceConnectionFactory factory = new LettuceConnectionFactory(config);
        factory.afterPropertiesSet();
        
        log.info("✅ 强制Redis连接工厂创建成功: {}:{}", redisHost, redisPort);
        return factory;
    }

    /**
     * 强制RedisTemplate
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        log.info("🔧 创建强制RedisTemplate");
        
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // Jackson序列化配置
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(om, Object.class);

        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

        // 设置序列化器
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        template.setValueSerializer(jackson2JsonRedisSerializer);
        template.setHashValueSerializer(jackson2JsonRedisSerializer);

        template.afterPropertiesSet();
        
        log.info("✅ 强制RedisTemplate创建成功");
        return template;
    }

    /**
     * 响应式StringRedisTemplate
     * Spring Cloud Gateway的RedisRateLimiter需要这个Bean
     */
    @Bean
    @Primary
    public ReactiveStringRedisTemplate reactiveStringRedisTemplate(RedisConnectionFactory connectionFactory) {
        log.info("🔧 创建ReactiveStringRedisTemplate（网关限流需要）");

        // LettuceConnectionFactory同时实现了RedisConnectionFactory和ReactiveRedisConnectionFactory
        if (connectionFactory instanceof ReactiveRedisConnectionFactory) {
            ReactiveRedisConnectionFactory reactiveFactory = (ReactiveRedisConnectionFactory) connectionFactory;
            ReactiveStringRedisTemplate template = new ReactiveStringRedisTemplate(reactiveFactory);

            log.info("✅ ReactiveStringRedisTemplate创建成功");
            return template;
        } else {
            throw new IllegalArgumentException("连接工厂不支持响应式操作");
        }
    }

    /**
     * 响应式RedisTemplate
     * 提供完整的响应式Redis操作支持
     */
    @Bean
    @Primary
    public ReactiveRedisTemplate<String, Object> reactiveRedisTemplate(RedisConnectionFactory connectionFactory) {
        log.info("🔧 创建ReactiveRedisTemplate");

        if (connectionFactory instanceof ReactiveRedisConnectionFactory) {
            ReactiveRedisConnectionFactory reactiveFactory = (ReactiveRedisConnectionFactory) connectionFactory;

            // 使用String序列化器
            StringRedisSerializer stringSerializer = new StringRedisSerializer();

            // 使用Jackson序列化器
            Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
            ObjectMapper om = new ObjectMapper();
            om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
            om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
            jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(om, Object.class);

            // 创建序列化上下文
            org.springframework.data.redis.serializer.RedisSerializationContext<String, Object> serializationContext =
                org.springframework.data.redis.serializer.RedisSerializationContext
                    .<String, Object>newSerializationContext()
                    .key(stringSerializer)
                    .value(jackson2JsonRedisSerializer)
                    .hashKey(stringSerializer)
                    .hashValue(jackson2JsonRedisSerializer)
                    .build();

            ReactiveRedisTemplate<String, Object> template = new ReactiveRedisTemplate<>(reactiveFactory, serializationContext);

            log.info("✅ ReactiveRedisTemplate创建成功");
            return template;
        } else {
            throw new IllegalArgumentException("连接工厂不支持响应式操作");
        }
    }
}
