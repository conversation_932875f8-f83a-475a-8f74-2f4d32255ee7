package com.wit.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.InetSocketAddress;
import java.util.Objects;

/**
 * 网关限流Key解析器配置
 *
 * 作用：
 * 1. 为Spring Cloud Gateway的RequestRateLimiter过滤器提供限流键解析
 * 2. 支持多种限流策略：IP限流、用户限流、API限流
 * 3. 与Redis配合实现分布式限流
 *
 * 限流策略：
 * - IP限流：根据客户端IP地址限流，防止单个IP过度请求
 * - 用户限流：根据用户ID限流，实现用户级别的访问控制
 * - API限流：根据API路径限流，保护特定接口
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class KeyResolverConfig {

    /**
     * IP 地址限流 Key 解析器
     * 根据客户端 IP 地址进行限流
     * 设置为主要的KeyResolver，用于默认限流
     */
    @Bean
    @Primary
    public KeyResolver ipKeyResolver() {
        return new KeyResolver() {
            @Override
            public Mono<String> resolve(ServerWebExchange exchange) {
                return Mono.fromCallable(() -> {
                    String clientIp = getClientIp(exchange);
                    log.debug("IP限流 - 客户端IP: {}", clientIp);
                    return clientIp;
                });
            }
        };
    }

    /**
     * 用户限流 Key 解析器
     * 根据用户ID进行限流
     */
    @Bean
    public KeyResolver userKeyResolver() {
        return new KeyResolver() {
            @Override
            public Mono<String> resolve(ServerWebExchange exchange) {
                return Mono.fromCallable(() -> {
                    // 从请求头中获取用户ID
                    String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
                    if (userId == null || userId.trim().isEmpty()) {
                        // 如果没有用户ID，使用IP作为fallback
                        userId = getClientIp(exchange);
                    }
                    log.debug("用户限流 - 用户ID: {}", userId);
                    return "user:" + userId;
                });
            }
        };
    }

    /**
     * 租户限流 Key 解析器
     * 根据租户ID进行限流
     */
    @Bean
    public KeyResolver tenantKeyResolver() {
        return new KeyResolver() {
            @Override
            public Mono<String> resolve(ServerWebExchange exchange) {
                return Mono.fromCallable(() -> {
                    // 从请求头中获取租户ID
                    String tenantId = exchange.getRequest().getHeaders().getFirst("X-Tenant-Id");
                    if (tenantId == null || tenantId.trim().isEmpty()) {
                        // 从路径中提取租户ID (如 /123/user/info)
                        String path = exchange.getRequest().getURI().getPath();
                        if (path.matches("^/\\d+/.*")) {
                            tenantId = path.split("/")[1];
                        } else {
                            tenantId = "default";
                        }
                    }
                    log.debug("租户限流 - 租户ID: {}", tenantId);
                    return "tenant:" + tenantId;
                });
            }
        };
    }

    /**
     * 组合限流 Key 解析器
     * 根据租户ID + 用户ID进行限流
     */
    @Bean
    public KeyResolver tenantUserKeyResolver() {
        return new KeyResolver() {
            @Override
            public Mono<String> resolve(ServerWebExchange exchange) {
                return Mono.fromCallable(() -> {
                    String tenantId = exchange.getRequest().getHeaders().getFirst("X-Tenant-Id");
                    String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
                    
                    if (tenantId == null || tenantId.trim().isEmpty()) {
                        String path = exchange.getRequest().getURI().getPath();
                        if (path.matches("^/\\d+/.*")) {
                            tenantId = path.split("/")[1];
                        } else {
                            tenantId = "default";
                        }
                    }
                    
                    if (userId == null || userId.trim().isEmpty()) {
                        userId = getClientIp(exchange);
                    }
                    
                    String key = "tenant:" + tenantId + ":user:" + userId;
                    log.debug("组合限流 - Key: {}", key);
                    return key;
                });
            }
        };
    }

    /**
     * API 路径限流 Key 解析器
     * 根据 API 路径进行限流
     */
    @Bean
    public KeyResolver apiKeyResolver() {
        return new KeyResolver() {
            @Override
            public Mono<String> resolve(ServerWebExchange exchange) {
                return Mono.fromCallable(() -> {
                    String path = exchange.getRequest().getURI().getPath();
                    String method = exchange.getRequest().getMethod().name();
                    String key = method + ":" + path;
                    log.debug("API限流 - Key: {}", key);
                    return key;
                });
            }
        };
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(ServerWebExchange exchange) {
        String clientIp = null;
        
        // 尝试从各种代理头中获取真实IP
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String header : headers) {
            clientIp = exchange.getRequest().getHeaders().getFirst(header);
            if (isValidIp(clientIp)) {
                // X-Forwarded-For 可能包含多个IP，取第一个
                if (clientIp.contains(",")) {
                    clientIp = clientIp.split(",")[0].trim();
                }
                break;
            }
        }
        
        // 如果都没有获取到，使用远程地址
        if (!isValidIp(clientIp)) {
            InetSocketAddress remoteAddress = exchange.getRequest().getRemoteAddress();
            if (remoteAddress != null) {
                clientIp = remoteAddress.getAddress().getHostAddress();
            }
        }
        
        // 最后的fallback
        if (!isValidIp(clientIp)) {
            clientIp = "unknown";
        }
        
        return clientIp;
    }

    /**
     * 验证IP地址是否有效
     */
    private boolean isValidIp(String ip) {
        return ip != null 
            && !ip.trim().isEmpty() 
            && !"unknown".equalsIgnoreCase(ip)
            && !"0:0:0:0:0:0:0:1".equals(ip)
            && !"127.0.0.1".equals(ip);
    }
}
