package com.wit.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;

import java.util.HashMap;
import java.util.Map;

/**
 * Spring Boot 3 现代化Nacos配置处理器
 *
 * 作用：
 * 1. 替代传统的bootstrap.yml配置方式
 * 2. 在Spring Boot启动的最早期（EnvironmentPostProcessor阶段）注入Nacos配置
 * 3. 通过高优先级配置源确保Nacos客户端使用正确的服务器地址
 *
 * 实现原理：
 * - EnvironmentPostProcessor在Spring容器初始化之前执行
 * - 通过addFirst()方法添加最高优先级的配置源
 * - 同时设置系统属性作为双重保障
 *
 * 这是Spring Boot 3推荐的现代化配置方式，无需bootstrap.yml
 *
 * <AUTHOR>
 */
@Slf4j
public class EarlyNacosConfig implements EnvironmentPostProcessor {

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        System.out.println("🚀 早期Nacos配置处理器启动");
        
        // 强制设置系统属性
        setSystemProperties();
        
        // 添加高优先级的配置源
        addHighPriorityPropertySource(environment);
        
        System.out.println("✅ 早期Nacos配置处理完成");
    }
    
    /**
     * 设置系统属性
     */
    private void setSystemProperties() {
        System.out.println("🔧 设置Nacos系统属性");
        
        // 强制设置所有Nacos相关的系统属性
        Map<String, String> nacosProps = new HashMap<>();
        nacosProps.put("spring.cloud.nacos.config.server-addr", "192.168.101.249:8848");
        nacosProps.put("spring.cloud.nacos.discovery.server-addr", "192.168.101.249:8848");
        nacosProps.put("spring.cloud.nacos.config.namespace", "1f290fcd-60d0-48d1-893c-0d2ffb30625d");
        nacosProps.put("spring.cloud.nacos.discovery.namespace", "1f290fcd-60d0-48d1-893c-0d2ffb30625d");
        nacosProps.put("spring.cloud.nacos.config.username", "nacos");
        nacosProps.put("spring.cloud.nacos.config.password", "nacos");
        nacosProps.put("spring.cloud.nacos.discovery.username", "nacos");
        nacosProps.put("spring.cloud.nacos.discovery.password", "nacos");
        nacosProps.put("spring.cloud.nacos.config.group", "DEFAULT_GROUP");
        nacosProps.put("spring.cloud.nacos.discovery.group", "DEFAULT_GROUP");
        nacosProps.put("spring.cloud.nacos.config.file-extension", "yml");
        nacosProps.put("spring.cloud.nacos.config.refresh-enabled", "true");
        
        // 设置到系统属性
        nacosProps.forEach((key, value) -> {
            System.setProperty(key, value);
            System.out.println("   - " + key + ": " + value);
        });
        
        // 额外的Nacos客户端属性
        System.setProperty("nacos.server-addr", "192.168.101.249:8848");
        System.setProperty("nacos.namespace", "1f290fcd-60d0-48d1-893c-0d2ffb30625d");
        System.setProperty("nacos.username", "nacos");
        System.setProperty("nacos.password", "nacos");
        
        System.out.println("✅ 系统属性设置完成");
    }
    
    /**
     * 添加高优先级的配置源
     */
    private void addHighPriorityPropertySource(ConfigurableEnvironment environment) {
        System.out.println("🔧 添加高优先级Nacos配置源");
        
        Map<String, Object> nacosConfig = new HashMap<>();
        nacosConfig.put("spring.cloud.nacos.config.server-addr", "192.168.101.249:8848");
        nacosConfig.put("spring.cloud.nacos.discovery.server-addr", "192.168.101.249:8848");
        nacosConfig.put("spring.cloud.nacos.config.namespace", "1f290fcd-60d0-48d1-893c-0d2ffb30625d");
        nacosConfig.put("spring.cloud.nacos.discovery.namespace", "1f290fcd-60d0-48d1-893c-0d2ffb30625d");
        nacosConfig.put("spring.cloud.nacos.config.username", "nacos");
        nacosConfig.put("spring.cloud.nacos.config.password", "nacos");
        nacosConfig.put("spring.cloud.nacos.discovery.username", "nacos");
        nacosConfig.put("spring.cloud.nacos.discovery.password", "nacos");
        nacosConfig.put("spring.cloud.nacos.config.group", "DEFAULT_GROUP");
        nacosConfig.put("spring.cloud.nacos.discovery.group", "DEFAULT_GROUP");
        nacosConfig.put("spring.cloud.nacos.config.file-extension", "yml");
        nacosConfig.put("spring.cloud.nacos.config.refresh-enabled", true);
        
        // 共享配置
        nacosConfig.put("spring.cloud.nacos.config.shared-configs[0].data-id", "common-redis.yml");
        nacosConfig.put("spring.cloud.nacos.config.shared-configs[0].group", "COMMON_GROUP");
        nacosConfig.put("spring.cloud.nacos.config.shared-configs[0].refresh", true);
        
        MapPropertySource propertySource = new MapPropertySource("earlyNacosConfig", nacosConfig);
        environment.getPropertySources().addFirst(propertySource);
        
        System.out.println("✅ 高优先级配置源添加完成");
    }
}
