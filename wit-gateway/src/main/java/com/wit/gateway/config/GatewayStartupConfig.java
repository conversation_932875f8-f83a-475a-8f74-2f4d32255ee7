package com.wit.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Spring Boot 3 现代化网关启动配置
 * 学习Spring Boot 3最新特性的最佳实践
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GatewayStartupConfig implements ApplicationRunner {

    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("🚀 Spring Boot 3 现代化网关启动初始化...");

        try {
            // 1. 检查服务发现状态
            checkServiceDiscovery();

            // 2. 检查Redis连接（暂时禁用，先让网关启动）
            // checkRedisConnection();
            log.info("⚠️ Redis连接检查已暂时禁用，网关将继续启动");

            // 3. 初始化网关缓存
            initializeGatewayCache();

            // 4. 预热路由规则
            warmupRoutes();

            // 5. 记录启动信息
            recordStartupInfo();

            log.info("✅ 网关启动后初始化任务完成！");

        } catch (Exception e) {
            log.error("❌ 网关启动后初始化任务失败", e);
            throw e;
        }
    }

    /**
     * 检查服务发现状态
     */
    private void checkServiceDiscovery() {
        try {
            List<String> services = discoveryClient.getServices();
            log.info("📡 服务发现状态检查:");
            log.info("   - 已注册服务数量: {}", services.size());
            
            if (!services.isEmpty()) {
                log.info("   - 服务列表: {}", services);
                
                // 检查关键服务
                String[] criticalServices = {"wit-auth", "wit-user", "wit-product", "wit-order"};
                for (String service : criticalServices) {
                    if (services.contains(service)) {
                        int instanceCount = discoveryClient.getInstances(service).size();
                        log.info("   - ✅ {} 服务可用 ({} 个实例)", service, instanceCount);
                    } else {
                        log.warn("   - ⚠️ {} 服务未发现", service);
                    }
                }
            } else {
                log.warn("   - ⚠️ 未发现任何注册服务");
            }
            
        } catch (Exception e) {
            log.error("服务发现状态检查失败", e);
            throw new RuntimeException("服务发现不可用", e);
        }
    }

    /**
     * 检查Redis连接
     */
    private void checkRedisConnection() {
        try {
            // 先打印连接工厂信息
            log.info("🔗 Redis连接检查开始:");
            if (redisTemplate.getConnectionFactory() instanceof org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory) {
                org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory factory =
                    (org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory) redisTemplate.getConnectionFactory();
                log.info("   - 尝试连接到: {}:{}", factory.getHostName(), factory.getPort());
                log.info("   - 数据库: {}", factory.getDatabase());
                log.info("   - 超时时间: {}", factory.getTimeout());
            }

            String pong = redisTemplate.getConnectionFactory()
                .getConnection()
                .ping();

            log.info("🔗 Redis连接状态检查:");
            log.info("   - 连接状态: ✅ 正常 ({})", pong);

            // 检查Redis中的数据
            long tenantCount = redisTemplate.keys("tenant:*").size();
            long sessionCount = redisTemplate.keys("user:session:*").size();

            log.info("   - 租户数据: {} 个", tenantCount);
            log.info("   - 用户会话: {} 个", sessionCount);

        } catch (Exception e) {
            log.error("🚨🚨🚨 Redis连接检查失败 🚨🚨🚨", e);

            // 打印详细的连接信息
            if (redisTemplate.getConnectionFactory() instanceof org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory) {
                org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory factory =
                    (org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory) redisTemplate.getConnectionFactory();
                log.error("🚨 连接失败详情:");
                log.error("   - 目标主机: {}", factory.getHostName());
                log.error("   - 目标端口: {}", factory.getPort());
                log.error("   - 数据库: {}", factory.getDatabase());
                log.error("   - 超时时间: {}", factory.getTimeout());

                if ("localhost".equals(factory.getHostName()) || "127.0.0.1".equals(factory.getHostName())) {
                    log.error("🚨🚨🚨 发现问题：正在尝试连接本地Redis！");
                    log.error("🚨🚨🚨 这可能是配置问题，请检查Redis配置！");
                }
            }

            throw new RuntimeException("Redis连接不可用", e);
        }
    }

    /**
     * 初始化网关缓存
     */
    private void initializeGatewayCache() {
        try {
            log.info("💾 初始化网关缓存...");
            
            // 设置网关启动时间
            String startupTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            redisTemplate.opsForValue().set("gateway:startup:time", startupTime);
            
            // 初始化网关状态
            redisTemplate.opsForValue().set("gateway:status", "RUNNING");
            
            // 初始化统计计数器
            redisTemplate.opsForValue().set("gateway:stats:requests:total", 0);
            redisTemplate.opsForValue().set("gateway:stats:errors:total", 0);
            
            log.info("   - ✅ 网关缓存初始化完成");
            
        } catch (Exception e) {
            log.error("网关缓存初始化失败", e);
            // 缓存初始化失败不应该阻止启动
        }
    }

    /**
     * 预热路由规则
     */
    private void warmupRoutes() {
        try {
            log.info("🔥 预热路由规则...");
            
            // 这里可以预加载一些路由规则到缓存
            // 例如：常用的租户路由映射
            
            // 模拟预热一些常用路由
            String[] commonRoutes = {
                "/*/auth/login",
                "/*/user/profile",
                "/*/product/list",
                "/*/order/list"
            };
            
            for (String route : commonRoutes) {
                redisTemplate.opsForSet().add("gateway:routes:warmed", route);
            }
            
            log.info("   - ✅ 路由预热完成 ({} 个路由)", commonRoutes.length);
            
        } catch (Exception e) {
            log.error("路由预热失败", e);
            // 路由预热失败不应该阻止启动
        }
    }

    /**
     * 记录启动信息
     */
    private void recordStartupInfo() {
        try {
            log.info("📝 记录启动信息...");
            
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            // 记录启动信息到Redis
            redisTemplate.opsForHash().put("gateway:startup:info", "timestamp", timestamp);
            redisTemplate.opsForHash().put("gateway:startup:info", "version", "1.0.0");
            redisTemplate.opsForHash().put("gateway:startup:info", "java.version", System.getProperty("java.version"));
            redisTemplate.opsForHash().put("gateway:startup:info", "spring.profiles", System.getProperty("spring.profiles.active", "default"));
            
            // 设置过期时间（7天）
            redisTemplate.expire("gateway:startup:info", java.time.Duration.ofDays(7));
            
            log.info("   - ✅ 启动信息记录完成");
            
        } catch (Exception e) {
            log.error("启动信息记录失败", e);
            // 记录失败不应该阻止启动
        }
    }
}
