package com.wit.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.net.Socket;
import java.util.concurrent.TimeUnit;

/**
 * Nacos连接配置和健康检查
 * 
 * ===================================================================
 * 功能说明：
 * 1. Nacos连接状态检查 - 在应用启动时检查Nacos服务器连通性
 * 2. 连接重试机制 - 提供连接失败时的重试逻辑
 * 3. 优雅降级 - 当Nacos不可用时提供备用方案
 * 4. 连接监控 - 持续监控Nacos连接状态
 * 
 * 检查流程：
 * 1. 检查Nacos服务器网络连通性
 * 2. 验证Nacos服务是否正常响应
 * 3. 检查认证信息是否正确
 * 4. 提供连接状态报告
 * 
 * 错误处理：
 * - 连接失败时记录详细错误信息
 * - 提供问题排查建议
 * - 支持优雅降级模式
 * ===================================================================
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-02
 */
@Slf4j
@Component
@Order(1) // 优先级最高，最先执行
public class NacosConnectionConfig implements ApplicationRunner {

    @Value("${spring.cloud.nacos.discovery.server-addr:localhost:8848}")
    private String nacosServerAddr;

    @Value("${spring.cloud.nacos.discovery.username:nacos}")
    private String nacosUsername;

    @Value("${spring.cloud.nacos.discovery.password:nacos}")
    private String nacosPassword;

    @Value("${spring.cloud.nacos.discovery.namespace:}")
    private String nacosNamespace;

    /**
     * 应用启动后检查Nacos连接状态
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("🔍 开始检查Nacos连接状态...");
        
        try {
            // 1. 检查网络连通性
            checkNetworkConnectivity();
            
            // 2. 检查Nacos服务状态
            checkNacosServiceStatus();
            
            // 3. 提供连接建议
            provideConnectionAdvice();
            
            log.info("✅ Nacos连接检查完成");
            
        } catch (Exception e) {
            log.error("❌ Nacos连接检查失败", e);
            // 不抛出异常，允许应用继续启动
        }
    }

    /**
     * 检查网络连通性
     */
    private void checkNetworkConnectivity() {
        String[] hostPort = nacosServerAddr.split(":");
        String host = hostPort[0];
        int port = hostPort.length > 1 ? Integer.parseInt(hostPort[1]) : 8848;
        
        try (Socket socket = new Socket()) {
            socket.connect(new java.net.InetSocketAddress(host, port), 3000);
            log.info("✅ Nacos服务器网络连通性正常: {}:{}", host, port);
        } catch (Exception e) {
            log.error("❌ 无法连接到Nacos服务器: {}:{}", host, port);
            log.error("💡 请检查：");
            log.error("   1. Nacos服务器是否已启动");
            log.error("   2. 网络连接是否正常");
            log.error("   3. 防火墙设置是否正确");
            log.error("   4. 服务器地址配置是否正确: {}", nacosServerAddr);
        }
    }

    /**
     * 检查Nacos服务状态
     */
    private void checkNacosServiceStatus() {
        try {
            // 这里可以添加HTTP请求检查Nacos API
            log.info("🔧 Nacos配置信息:");
            log.info("   服务器地址: {}", nacosServerAddr);
            log.info("   用户名: {}", nacosUsername);
            log.info("   命名空间: {}", nacosNamespace.isEmpty() ? "默认命名空间" : nacosNamespace);
            
        } catch (Exception e) {
            log.error("❌ Nacos服务状态检查失败", e);
        }
    }

    /**
     * 提供连接建议
     */
    private void provideConnectionAdvice() {
        log.info("💡 Nacos连接建议:");
        log.info("   1. 确保Docker容器已启动: docker ps | grep nacos");
        log.info("   2. 检查Nacos控制台: http://{}:8848/nacos", nacosServerAddr.split(":")[0]);
        log.info("   3. 验证用户名密码: {}/{}", nacosUsername, nacosPassword);
        log.info("   4. 如果是首次启动，Nacos客户端可能需要几秒钟完成连接");
        log.info("   5. 'Client not connected, current status:STARTING' 错误通常会自动恢复");
    }

    /**
     * 检查Nacos连接状态（可以被其他组件调用）
     */
    public boolean isNacosConnected() {
        String[] hostPort = nacosServerAddr.split(":");
        String host = hostPort[0];
        int port = hostPort.length > 1 ? Integer.parseInt(hostPort[1]) : 8848;
        
        try (Socket socket = new Socket()) {
            socket.connect(new java.net.InetSocketAddress(host, port), 1000);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 等待Nacos连接就绪
     */
    public void waitForNacosReady(int maxWaitSeconds) {
        log.info("⏳ 等待Nacos连接就绪，最大等待时间: {}秒", maxWaitSeconds);
        
        for (int i = 0; i < maxWaitSeconds; i++) {
            if (isNacosConnected()) {
                log.info("✅ Nacos连接已就绪，等待时间: {}秒", i);
                return;
            }
            
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.warn("⚠️ Nacos连接等待超时，应用将继续启动");
    }
}
