package com.wit.gateway.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 网关监控指标配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class MetricsConfig {

    @Autowired
    @Lazy
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 自定义指标注册器
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> {
            registry.config().commonTags(
                "application", "wit-gateway",
                "version", "1.0.0"
            );
            
            // 注册自定义指标
            registerCustomMetrics(registry);
        };
    }

    /**
     * 注册自定义指标
     */
    private void registerCustomMetrics(MeterRegistry registry) {
        // 活跃连接数
        registry.gauge("gateway.connections.active", this, MetricsConfig::getActiveConnections);
        
        // 租户请求计数
        registry.gauge("gateway.tenants.active", this, MetricsConfig::getActiveTenants);
        
        // Redis连接状态
        registry.gauge("gateway.redis.connected", this, MetricsConfig::getRedisConnectionStatus);
        
        // 请求处理时间
        Timer.builder("gateway.request.duration")
            .description("Gateway request processing time")
            .register(registry);
        
        // 错误率
        registry.counter("gateway.errors.total", "type", "client");
        registry.counter("gateway.errors.total", "type", "server");
        registry.counter("gateway.errors.total", "type", "timeout");
        
        log.info("✅ 网关自定义指标注册完成");
    }

    /**
     * 获取活跃连接数（模拟）
     */
    private double getActiveConnections() {
        // 这里可以实现真实的连接数统计
        return Math.random() * 100;
    }

    /**
     * 获取活跃租户数
     */
    private double getActiveTenants() {
        try {
            // 从Redis获取活跃租户数
            String pattern = "tenant:active:*";
            return redisTemplate.keys(pattern).size();
        } catch (Exception e) {
            log.warn("获取活跃租户数失败", e);
            return 0;
        }
    }

    /**
     * 获取Redis连接状态
     */
    private double getRedisConnectionStatus() {
        try {
            redisTemplate.opsForValue().get("health:check");
            return 1.0; // 连接正常
        } catch (Exception e) {
            return 0.0; // 连接异常
        }
    }

    /**
     * 网关指标收集器
     */
    @Bean
    public GatewayMetricsCollector gatewayMetricsCollector(MeterRegistry meterRegistry) {
        return new GatewayMetricsCollector(meterRegistry);
    }

    /**
     * 网关指标收集器实现
     */
    public static class GatewayMetricsCollector {
        private final MeterRegistry meterRegistry;
        private final AtomicLong requestCount = new AtomicLong(0);
        private final AtomicLong errorCount = new AtomicLong(0);

        public GatewayMetricsCollector(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            
            // 注册计数器
            meterRegistry.gauge("gateway.requests.total", requestCount);
            meterRegistry.gauge("gateway.errors.count", errorCount);
        }

        public void incrementRequestCount() {
            requestCount.incrementAndGet();
            meterRegistry.counter("gateway.requests", "status", "total").increment();
        }

        public void incrementErrorCount(String errorType) {
            errorCount.incrementAndGet();
            meterRegistry.counter("gateway.errors", "type", errorType).increment();
        }

        public void recordRequestDuration(long durationMs, String tenantId, String service) {
            Timer.Sample sample = Timer.start(meterRegistry);
            sample.stop(Timer.builder("gateway.request.duration")
                .tag("tenant", tenantId)
                .tag("service", service)
                .register(meterRegistry));
        }

        public long getRequestCount() {
            return requestCount.get();
        }

        public long getErrorCount() {
            return errorCount.get();
        }
    }
}
