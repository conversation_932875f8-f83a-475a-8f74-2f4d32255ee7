package com.wit.gateway.config;

import com.wit.gateway.filter.*;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * 网关过滤器配置
 * 统一管理所有过滤器的顺序和配置
 * 
 * <AUTHOR>
 */
@Configuration
public class FilterConfig {

    /**
     * 配置过滤器执行顺序
     * 
     * 执行顺序（数字越小优先级越高）：
     * 1. MonitoringFilter (0) - 监控过滤器，记录请求开始时间
     * 2. RateLimitFilter (50) - 限流过滤器，防止恶意请求
     * 3. TenantRouteFilter (100) - 租户路由过滤器，解析租户信息
     * 4. AuthFilter (150) - 认证过滤器，验证用户身份
     * 5. CircuitBreakerFilter (200) - 熔断器过滤器，服务保护
     */
    
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder,
                                         MonitoringFilter monitoringFilter,
                                         RateLimitFilter rateLimitFilter,
                                         TenantRouteFilter tenantRouteFilter,
                                         AuthFilter authFilter,
                                         CircuitBreakerFilter circuitBreakerFilter) {
        return builder.routes()
            // 健康检查路由 - 不需要任何过滤器
            .route("health-check", r -> r
                .path("/gateway/health", "/actuator/health")
                .uri("no://op"))
            
            // 网关信息路由 - 只需要监控
            .route("gateway-info", r -> r
                .path("/gateway/info", "/gateway/tenants/**", "/gateway/test/**")
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config())))
                .uri("no://op"))
            
            // 认证相关路由 - 监控 + 限流
            .route("auth-routes", r -> r
                .path("/*/auth/**")
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(rateLimitFilter.apply(createRateLimitConfig(100, 60))))
                .uri("lb://wit-auth"))
            
            // 公开API路由 - 监控 + 限流 + 租户路由
            .route("public-api", r -> r
                .path("/*/product/list", "/*/product/detail/**", "/*/search/**")
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(rateLimitFilter.apply(createRateLimitConfig(200, 60)))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config())))
                .uri("lb://wit-product"))
            
            // 受保护的API路由 - 全部过滤器
            .route("protected-api", r -> r
                .path("/**")
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(rateLimitFilter.apply(createRateLimitConfig(50, 60)))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .filter(authFilter.apply(new AuthFilter.Config()))
                    .filter(circuitBreakerFilter.apply(createCircuitBreakerConfig())))
                .uri("lb://default-service"))
            
            .build();
    }
    
    /**
     * 创建限流配置
     */
    private RateLimitFilter.Config createRateLimitConfig(int limit, int windowSeconds) {
        RateLimitFilter.Config config = new RateLimitFilter.Config();
        config.setLimit(limit);
        config.setWindowSize(windowSeconds);
        config.setEnabled(true);
        return config;
    }
    
    /**
     * 创建熔断器配置
     */
    private CircuitBreakerFilter.Config createCircuitBreakerConfig() {
        CircuitBreakerFilter.Config config = new CircuitBreakerFilter.Config();
        config.setFailureThreshold(0.5);
        config.setMinimumRequests(10);
        config.setStatisticsWindow(60);
        config.setSleepWindow(30);
        config.setTimeout(5000);
        config.setEnabled(true);
        return config;
    }
}
