package com.wit.gateway.config;

import com.wit.gateway.filter.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 网关核心路由配置
 *
 * 作用：
 * 1. 配置多租户路由规则（/{tenantId}/{service}/**）
 * 2. 整合所有网关过滤器（认证、限流、监控、熔断等）
 * 3. 定义服务发现和负载均衡规则
 *
 * 路由策略：
 * - 多租户路由：支持租户隔离的服务访问
 * - 服务发现：基于Nacos的动态服务发现
 * - 过滤器链：认证 -> 限流 -> 监控 -> 熔断 -> 路由
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class GatewayConfig {

    /**
     * 配置多租户路由（整合所有过滤器）
     */
    @Bean
    public RouteLocator tenantRouteLocator(RouteLocatorBuilder builder,
                                         TenantRouteFilter tenantRouteFilter,
                                         MonitoringFilter monitoringFilter,
                                         AuthFilter authFilter,
                                         RateLimitFilter rateLimitFilter,
                                         CircuitBreakerFilter circuitBreakerFilter) {
        return builder.routes()
            // 认证服务路由（公开服务，不需要认证）
            .route("tenant-auth", r -> r
                .path("/{tenantId}/auth/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/auth/.*$");
                })
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/auth
                )
                .uri("lb://wit-auth")
            )
            
            // 用户服务路由（需要认证）
            .route("tenant-user", r -> r
                .path("/{tenantId}/user/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/user/.*$");
                })
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(authFilter.apply(new AuthFilter.Config()))
                    .filter(rateLimitFilter.apply(createRateLimitConfig("tenant", 100, 60)))
                    .filter(circuitBreakerFilter.apply(createCircuitBreakerConfig()))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/user
                )
                .uri("lb://wit-user")
            )
            
            // 商品服务路由（部分接口公开）
            .route("tenant-product", r -> r
                .path("/{tenantId}/product/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/product/.*$");
                })
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(authFilter.apply(createAuthConfig(false))) // 商品浏览不需要认证
                    .filter(rateLimitFilter.apply(createRateLimitConfig("tenant", 200, 60)))
                    .filter(circuitBreakerFilter.apply(createCircuitBreakerConfig()))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/product
                )
                .uri("lb://wit-product")
            )
            
            // 订单服务路由（需要认证，高限流）
            .route("tenant-order", r -> r
                .path("/{tenantId}/order/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/order/.*$");
                })
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(authFilter.apply(createAuthConfig(true)))
                    .filter(rateLimitFilter.apply(createRateLimitConfig("tenant", 50, 60)))
                    .filter(circuitBreakerFilter.apply(createCircuitBreakerConfig()))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/order
                )
                .uri("lb://wit-order")
            )
            
            // 购物车服务路由
            .route("tenant-cart", r -> r
                .path("/{tenantId}/cart/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/cart/.*$");
                })
                .filters(f -> f
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/cart
                )
                .uri("lb://wit-cart")
            )
            
            // 支付服务路由
            .route("tenant-payment", r -> r
                .path("/{tenantId}/payment/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/payment/.*$");
                })
                .filters(f -> f
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/payment
                )
                .uri("lb://wit-payment")
            )
            
            // 库存服务路由
            .route("tenant-inventory", r -> r
                .path("/{tenantId}/inventory/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/inventory/.*$");
                })
                .filters(f -> f
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/inventory
                )
                .uri("lb://wit-inventory")
            )
            
            // 文件服务路由
            .route("tenant-file", r -> r
                .path("/{tenantId}/file/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/file/.*$");
                })
                .filters(f -> f
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/file
                )
                .uri("lb://wit-file")
            )
            
            .build();
    }

    /**
     * 创建限流配置
     */
    private RateLimitFilter.Config createRateLimitConfig(String keyType, int limit, int windowSize) {
        RateLimitFilter.Config config = new RateLimitFilter.Config();
        config.setKeyType(keyType);
        config.setLimit(limit);
        config.setWindowSize(windowSize);
        config.setEnabled(true);
        return config;
    }

    /**
     * 创建熔断器配置
     */
    private CircuitBreakerFilter.Config createCircuitBreakerConfig() {
        CircuitBreakerFilter.Config config = new CircuitBreakerFilter.Config();
        config.setFailureThreshold(0.5);
        config.setMinimumRequests(10);
        config.setStatisticsWindow(60);
        config.setSleepWindow(30);
        config.setTimeout(5000);
        config.setEnabled(true);
        return config;
    }

    /**
     * 创建认证配置
     */
    private AuthFilter.Config createAuthConfig(boolean enabled) {
        AuthFilter.Config config = new AuthFilter.Config();
        config.setEnabled(enabled);
        config.setStrictTenantValidation(true);
        config.setCheckUserSession(false);
        return config;
    }
}
