package com.wit.gateway.config;

import com.wit.gateway.filter.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 网关核心路由配置类
 *
 * ===================================================================
 * 功能说明：
 * 1. 多租户路由配置 - 支持 /{tenantId}/{service}/** 格式的路由
 * 2. 过滤器链整合 - 统一管理认证、限流、监控、熔断等过滤器
 * 3. 服务发现集成 - 基于Nacos的动态服务发现和负载均衡
 * 4. 路由规则定义 - 为不同服务配置不同的路由策略
 *
 * 路由策略：
 * - 认证服务：公开访问，仅监控和租户路由
 * - 用户服务：需要认证，包含限流和熔断
 * - 商品服务：部分公开，支持浏览和管理分离
 * - 其他服务：完整的过滤器链保护
 *
 * 过滤器执行顺序：
 * 1. MonitoringFilter - 请求监控和追踪
 * 2. AuthFilter - 身份认证和授权
 * 3. RateLimitFilter - 请求限流保护
 * 4. CircuitBreakerFilter - 熔断器保护
 * 5. TenantRouteFilter - 租户路由解析
 *
 * 多租户支持：
 * - 路由格式：/{tenantId}/{service}/**
 * - 示例：/3921/user/api/v1/profile
 * - 租户隔离：每个租户独立的服务访问
 * ===================================================================
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Configuration
public class GatewayConfig {

    /**
     * 配置多租户路由定位器
     *
     * 整合所有网关过滤器，为不同的服务配置不同的过滤器组合：
     * - 认证服务：公开访问，仅需监控和租户路由
     * - 用户服务：需要完整的认证和保护
     * - 商品服务：部分公开，支持匿名浏览
     * - 其他服务：完整的过滤器链保护
     *
     * @param builder 路由构建器
     * @param tenantRouteFilter 租户路由过滤器
     * @param monitoringFilter 监控过滤器
     * @param authFilter 认证过滤器
     * @param rateLimitFilter 限流过滤器
     * @param circuitBreakerFilter 熔断器过滤器
     * @return 配置好的路由定位器
     */
    @Bean
    public RouteLocator tenantRouteLocator(RouteLocatorBuilder builder,
                                         TenantRouteFilter tenantRouteFilter,
                                         MonitoringFilter monitoringFilter,
                                         AuthFilter authFilter,
                                         RateLimitFilter rateLimitFilter,
                                         CircuitBreakerFilter circuitBreakerFilter) {
        return builder.routes()
            // ===================================================================
            // 认证服务路由 - 公开服务，不需要认证
            // 路径：/{tenantId}/auth/**
            // 示例：/3921/auth/login, /3921/auth/register
            // 过滤器：监控 + 租户路由（不包含认证和限流）
            // ===================================================================
            .route("tenant-auth", r -> r
                .path("/{tenantId}/auth/**")                    // 匹配租户认证路径
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/auth/.*$");     // 验证租户ID为数字
                })
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))  // 请求监控
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config())) // 租户路由解析
                    .stripPrefix(2)                             // 移除 /{tenantId}/auth 前缀
                )
                .uri("lb://wit-auth")                           // 负载均衡到认证服务
            )
            
            // 用户服务路由（需要认证）
            .route("tenant-user", r -> r
                .path("/{tenantId}/user/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/user/.*$");
                })
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(authFilter.apply(new AuthFilter.Config()))
                    .filter(rateLimitFilter.apply(createRateLimitConfig("tenant", 100, 60)))
                    .filter(circuitBreakerFilter.apply(createCircuitBreakerConfig()))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/user
                )
                .uri("lb://wit-user")
            )
            
            // 商品服务路由（部分接口公开）
            .route("tenant-product", r -> r
                .path("/{tenantId}/product/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/product/.*$");
                })
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(authFilter.apply(createAuthConfig(false))) // 商品浏览不需要认证
                    .filter(rateLimitFilter.apply(createRateLimitConfig("tenant", 200, 60)))
                    .filter(circuitBreakerFilter.apply(createCircuitBreakerConfig()))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/product
                )
                .uri("lb://wit-product")
            )
            
            // 订单服务路由（需要认证，高限流）
            .route("tenant-order", r -> r
                .path("/{tenantId}/order/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/order/.*$");
                })
                .filters(f -> f
                    .filter(monitoringFilter.apply(new MonitoringFilter.Config()))
                    .filter(authFilter.apply(createAuthConfig(true)))
                    .filter(rateLimitFilter.apply(createRateLimitConfig("tenant", 50, 60)))
                    .filter(circuitBreakerFilter.apply(createCircuitBreakerConfig()))
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/order
                )
                .uri("lb://wit-order")
            )
            
            // 购物车服务路由
            .route("tenant-cart", r -> r
                .path("/{tenantId}/cart/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/cart/.*$");
                })
                .filters(f -> f
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/cart
                )
                .uri("lb://wit-cart")
            )
            
            // 支付服务路由
            .route("tenant-payment", r -> r
                .path("/{tenantId}/payment/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/payment/.*$");
                })
                .filters(f -> f
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/payment
                )
                .uri("lb://wit-payment")
            )
            
            // 库存服务路由
            .route("tenant-inventory", r -> r
                .path("/{tenantId}/inventory/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/inventory/.*$");
                })
                .filters(f -> f
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/inventory
                )
                .uri("lb://wit-inventory")
            )
            
            // 文件服务路由
            .route("tenant-file", r -> r
                .path("/{tenantId}/file/**")
                .and()
                .predicate(exchange -> {
                    String path = exchange.getRequest().getURI().getPath();
                    return path.matches("^/\\d+/file/.*$");
                })
                .filters(f -> f
                    .filter(tenantRouteFilter.apply(new TenantRouteFilter.Config()))
                    .stripPrefix(2) // 移除 /{tenantId}/file
                )
                .uri("lb://wit-file")
            )
            
            .build();
    }

    /**
     * 创建限流配置
     */
    private RateLimitFilter.Config createRateLimitConfig(String keyType, int limit, int windowSize) {
        RateLimitFilter.Config config = new RateLimitFilter.Config();
        config.setKeyType(keyType);
        config.setLimit(limit);
        config.setWindowSize(windowSize);
        config.setEnabled(true);
        return config;
    }

    /**
     * 创建熔断器配置
     */
    private CircuitBreakerFilter.Config createCircuitBreakerConfig() {
        CircuitBreakerFilter.Config config = new CircuitBreakerFilter.Config();
        config.setFailureThreshold(0.5);
        config.setMinimumRequests(10);
        config.setStatisticsWindow(60);
        config.setSleepWindow(30);
        config.setTimeout(5000);
        config.setEnabled(true);
        return config;
    }

    /**
     * 创建认证配置
     */
    private AuthFilter.Config createAuthConfig(boolean enabled) {
        AuthFilter.Config config = new AuthFilter.Config();
        config.setEnabled(enabled);
        config.setStrictTenantValidation(true);
        config.setCheckUserSession(false);
        return config;
    }
}
