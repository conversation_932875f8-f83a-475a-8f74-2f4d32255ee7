package com.wit.gateway.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 租户管理服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class TenantService {

    private static final String TENANT_KEY_PREFIX = "tenant:";
    private static final String TENANT_CONFIG_KEY = "tenant:config:";
    private static final long DEFAULT_CACHE_EXPIRE = 3600; // 1小时

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 验证租户是否存在
     */
    public boolean validateTenant(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            return false;
        }

        try {
            String key = TENANT_KEY_PREFIX + tenantId;
            Boolean exists = redisTemplate.hasKey(key);
            
            if (exists == null || !exists) {
                // 如果Redis中不存在，可以从数据库查询并缓存
                // 这里先简单返回true，实际项目中应该查询数据库
                log.debug("租户 {} 不在缓存中，默认允许访问", tenantId);
                return true;
            }
            
            return true;
        } catch (Exception e) {
            log.error("验证租户失败: {}", tenantId, e);
            return false;
        }
    }

    /**
     * 获取租户配置
     */
    public TenantConfig getTenantConfig(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            return null;
        }

        try {
            String key = TENANT_CONFIG_KEY + tenantId;
            Object config = redisTemplate.opsForValue().get(key);
            
            if (config instanceof TenantConfig) {
                return (TenantConfig) config;
            }
            
            // 如果缓存中没有，创建默认配置
            TenantConfig defaultConfig = createDefaultConfig(tenantId);
            cacheTenantConfig(tenantId, defaultConfig);
            
            return defaultConfig;
        } catch (Exception e) {
            log.error("获取租户配置失败: {}", tenantId, e);
            return createDefaultConfig(tenantId);
        }
    }

    /**
     * 缓存租户配置
     */
    public void cacheTenantConfig(String tenantId, TenantConfig config) {
        try {
            String key = TENANT_CONFIG_KEY + tenantId;
            redisTemplate.opsForValue().set(key, config, DEFAULT_CACHE_EXPIRE, TimeUnit.SECONDS);
            log.debug("缓存租户配置: {}", tenantId);
        } catch (Exception e) {
            log.error("缓存租户配置失败: {}", tenantId, e);
        }
    }

    /**
     * 获取所有活跃租户
     */
    public Set<String> getActiveTenants() {
        try {
            String pattern = TENANT_KEY_PREFIX + "*";
            Set<String> keys = redisTemplate.keys(pattern);

            if (keys != null) {
                return keys.stream()
                    .map(key -> key.substring(TENANT_KEY_PREFIX.length()))
                    .collect(Collectors.toSet());
            }

            return Set.of();
        } catch (Exception e) {
            log.error("获取活跃租户列表失败", e);
            return Set.of();
        }
    }

    /**
     * 创建默认租户配置
     */
    private TenantConfig createDefaultConfig(String tenantId) {
        TenantConfig config = new TenantConfig();
        config.setTenantId(tenantId);
        config.setEnabled(true);
        config.setMaxRequestsPerSecond(1000);
        config.setMaxConcurrentRequests(100);
        return config;
    }

    /**
     * 租户配置类
     */
    public static class TenantConfig {
        private String tenantId;
        private boolean enabled = true;
        private int maxRequestsPerSecond = 1000;
        private int maxConcurrentRequests = 100;

        // Getters and Setters
        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getMaxRequestsPerSecond() {
            return maxRequestsPerSecond;
        }

        public void setMaxRequestsPerSecond(int maxRequestsPerSecond) {
            this.maxRequestsPerSecond = maxRequestsPerSecond;
        }

        public int getMaxConcurrentRequests() {
            return maxConcurrentRequests;
        }

        public void setMaxConcurrentRequests(int maxConcurrentRequests) {
            this.maxConcurrentRequests = maxConcurrentRequests;
        }
    }
}
