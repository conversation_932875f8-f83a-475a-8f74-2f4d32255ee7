package com.wit.gateway.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wit.gateway.util.GatewayJwtUtil;
import io.jsonwebtoken.Claims;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

/**
 * 网关认证授权过滤器
 *
 * ===================================================================
 * 功能说明：
 * 1. JWT Token验证 - 验证请求中的JWT令牌有效性
 * 2. 用户身份认证 - 解析用户信息并验证身份
 * 3. 权限检查 - 基于角色和权限进行访问控制
 * 4. 白名单支持 - 配置无需认证的公开路径
 * 5. 用户信息传递 - 将用户信息添加到请求头传递给下游服务
 *
 * 认证流程：
 * 1. 检查请求路径是否在白名单中
 * 2. 从请求头中提取JWT Token
 * 3. 验证Token的有效性和完整性
 * 4. 解析Token中的用户信息
 * 5. 检查用户会话状态（可选）
 * 6. 将用户信息添加到请求头
 *
 * 白名单路径：
 * - /api/auth/** - 认证相关接口
 * - /api/product/list - 商品列表（公开浏览）
 * - /api/search/** - 搜索接口
 * - /health, /actuator/** - 健康检查和监控
 *
 * 错误处理：
 * - Token缺失：返回401未授权
 * - Token无效：返回401未授权
 * - Token过期：返回401未授权
 * - 权限不足：返回403禁止访问
 * ===================================================================
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class AuthFilter extends AbstractGatewayFilterFactory<AuthFilter.Config> {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private GatewayJwtUtil jwtUtil;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 白名单路径（不需要认证的路径）
     */
    private static final List<String> WHITE_LIST = Arrays.asList(
        "/*/auth/login",
        "/*/auth/register", 
        "/*/auth/captcha",
        "/*/file/upload",
        "/*/product/list",
        "/*/product/detail",
        "/gateway/health",
        "/gateway/info",
        "/actuator/**"
    );

    public AuthFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (!config.isEnabled()) {
                return chain.filter(exchange);
            }

            String path = exchange.getRequest().getURI().getPath();
            
            // 检查是否在白名单中
            if (isWhiteListPath(path)) {
                log.debug("白名单路径，跳过认证: {}", path);
                return chain.filter(exchange);
            }

            // 获取token
            String token = extractToken(exchange.getRequest());
            if (!StringUtils.hasText(token)) {
                log.warn("缺少认证token: {}", path);
                return handleAuthError(exchange, "缺少认证token", HttpStatus.UNAUTHORIZED);
            }

            // 验证token
            return validateToken(token, exchange)
                .flatMap(userInfo -> {
                    if (userInfo != null) {
                        // 添加用户信息到请求头
                        ServerHttpRequest modifiedRequest = exchange.getRequest().mutate()
                            .header("X-User-Id", userInfo.getUserId())
                            .header("X-Username", userInfo.getUsername())
                            .header("X-User-Roles", String.join(",", userInfo.getRoles()))
                            .build();
                        
                        ServerWebExchange modifiedExchange = exchange.mutate()
                            .request(modifiedRequest)
                            .build();
                        
                        return chain.filter(modifiedExchange);
                    } else {
                        return handleAuthError(exchange, "token无效或已过期", HttpStatus.UNAUTHORIZED);
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("认证过程中发生错误: {}", throwable.getMessage());
                    return handleAuthError(exchange, "认证失败", HttpStatus.UNAUTHORIZED);
                });
        };
    }

    /**
     * 检查是否为白名单路径
     */
    private boolean isWhiteListPath(String path) {
        return WHITE_LIST.stream().anyMatch(pattern -> {
            // 简单的通配符匹配
            if (pattern.endsWith("/**")) {
                String prefix = pattern.substring(0, pattern.length() - 3);
                return path.startsWith(prefix);
            } else if (pattern.contains("*")) {
                // 支持 /*/auth/login 这种格式
                String regex = pattern.replace("*", "[^/]+");
                return path.matches(regex);
            } else {
                return path.equals(pattern);
            }
        });
    }

    /**
     * 提取token
     */
    private String extractToken(ServerHttpRequest request) {
        // 从Authorization header获取
        String authHeader = request.getHeaders().getFirst("Authorization");
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        // 从query参数获取
        String tokenParam = request.getQueryParams().getFirst("token");
        if (StringUtils.hasText(tokenParam)) {
            return tokenParam;
        }

        return null;
    }

    /**
     * 验证token
     */
    private Mono<UserInfo> validateToken(String token, ServerWebExchange exchange) {
        return Mono.fromCallable(() -> {
            try {
                // 检查token是否在黑名单中
                String blacklistKey = "token:blacklist:" + token;
                Boolean isBlacklisted = redisTemplate.hasKey(blacklistKey);
                if (isBlacklisted != null && isBlacklisted) {
                    log.warn("token已被加入黑名单: {}", token);
                    return null;
                }

                // 验证JWT token
                if (!jwtUtil.validateToken(token)) {
                    log.warn("JWT token验证失败");
                    return null;
                }

                // 解析JWT token
                Claims claims = jwtUtil.parseToken(token);
                String userId = claims.getSubject();
                String username = claims.get("username", String.class);
                String tenantId = claims.get("tenantId", String.class);
                @SuppressWarnings("unchecked")
                List<String> roles = claims.get("roles", List.class);

                // 验证租户ID是否匹配
                String pathTenantId = extractTenantIdFromPath(exchange.getRequest().getURI().getPath());
                if (StringUtils.hasText(pathTenantId) && !pathTenantId.equals(tenantId)) {
                    log.warn("租户ID不匹配 - token中: {}, 路径中: {}", tenantId, pathTenantId);
                    return null;
                }

                // 检查用户是否在线（可选）
                String userSessionKey = "user:session:" + userId;
                String sessionToken = (String) redisTemplate.opsForValue().get(userSessionKey);
                if (sessionToken != null && !sessionToken.equals(token)) {
                    log.warn("用户在其他地方登录，当前token已失效: {}", userId);
                    return null;
                }

                UserInfo userInfo = new UserInfo();
                userInfo.setUserId(userId);
                userInfo.setUsername(username);
                userInfo.setTenantId(tenantId);
                userInfo.setRoles(roles != null ? roles : Arrays.asList("USER"));

                log.debug("token验证成功: userId={}, tenantId={}", userId, tenantId);
                return userInfo;

            } catch (Exception e) {
                log.error("token验证失败: {}", e.getMessage());
                return null;
            }
        });
    }

    /**
     * 从路径中提取租户ID
     */
    private String extractTenantIdFromPath(String path) {
        if (path.matches("^/(\\d+)/.*")) {
            return path.split("/")[1];
        }
        return null;
    }

    /**
     * 处理认证错误
     */
    private Mono<Void> handleAuthError(ServerWebExchange exchange, String message, HttpStatus status) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        String errorBody = String.format(
            "{\"code\":%d,\"message\":\"%s\",\"timestamp\":%d}",
            status.value(), message, System.currentTimeMillis()
        );

        return response.writeWith(
            Mono.just(response.bufferFactory().wrap(errorBody.getBytes()))
        );
    }



    /**
     * 用户信息类
     */
    @Data
    public static class UserInfo {
        private String userId;
        private String username;
        private String tenantId;
        private List<String> roles;
    }

    /**
     * 配置类
     */
    @Data
    public static class Config {
        /**
         * 是否启用认证
         */
        private boolean enabled = true;

        /**
         * 是否启用严格的租户验证
         */
        private boolean strictTenantValidation = true;

        /**
         * 是否检查用户会话
         */
        private boolean checkUserSession = false;

        /**
         * 自定义白名单路径
         */
        private List<String> customWhiteList;
    }
}
