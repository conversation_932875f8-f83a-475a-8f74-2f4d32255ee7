package com.wit.gateway.filter;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 网关监控过滤器
 *
 * ===================================================================
 * 功能说明：
 * 1. 请求追踪 - 为每个请求生成唯一ID，便于日志追踪
 * 2. 性能监控 - 记录请求处理时间，识别慢请求
 * 3. 指标收集 - 统计请求数量、成功率、错误率等指标
 * 4. 日志记录 - 详细记录请求和响应信息
 * 5. 租户统计 - 按租户维度统计访问情况
 *
 * 监控指标：
 * - 请求总数：按租户、服务、方法统计
 * - 响应时间：平均响应时间、最大响应时间
 * - 成功率：2xx状态码的请求比例
 * - 错误率：4xx、5xx状态码的请求比例
 * - 慢请求：超过阈值的请求统计
 *
 * 数据存储：
 * - Redis：实时指标数据，支持过期清理
 * - 日志：详细的请求日志，便于问题排查
 * - 内存：临时计算数据，提高性能
 *
 * 追踪信息：
 * - X-Request-Id：请求唯一标识
 * - X-Start-Time：请求开始时间
 * - X-Tenant-Id：租户标识
 * - X-Service-Name：目标服务名称
 * ===================================================================
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class MonitoringFilter extends AbstractGatewayFilterFactory<MonitoringFilter.Config> {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public MonitoringFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (!config.isEnabled()) {
                return chain.filter(exchange);
            }

            // 生成请求ID
            String requestId = generateRequestId();
            long startTime = System.currentTimeMillis();
            
            // 获取请求信息
            ServerHttpRequest request = exchange.getRequest();
            String tenantId = getTenantId(exchange);
            String serviceName = getServiceName(exchange);
            String clientIp = getClientIp(exchange);
            String userAgent = request.getHeaders().getFirst("User-Agent");
            String method = request.getMethod().name();
            String path = request.getURI().getPath();
            String query = request.getURI().getQuery();

            // 添加追踪信息到请求头
            ServerHttpRequest modifiedRequest = request.mutate()
                .header("X-Request-Id", requestId)
                .header("X-Start-Time", String.valueOf(startTime))
                .build();

            ServerWebExchange modifiedExchange = exchange.mutate()
                .request(modifiedRequest)
                .build();

            // 记录请求开始
            logRequestStart(requestId, tenantId, serviceName, method, path, query, clientIp, userAgent);

            return chain.filter(modifiedExchange)
                .doOnSuccess(aVoid -> {
                    // 请求成功
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;
                    int statusCode = exchange.getResponse().getStatusCode().value();
                    
                    logRequestEnd(requestId, tenantId, serviceName, statusCode, duration, true);
                    recordMetrics(tenantId, serviceName, method, statusCode, duration, config);
                })
                .doOnError(throwable -> {
                    // 请求失败
                    long endTime = System.currentTimeMillis();
                    long duration = endTime - startTime;
                    
                    logRequestEnd(requestId, tenantId, serviceName, 500, duration, false);
                    recordMetrics(tenantId, serviceName, method, 500, duration, config);
                    
                    log.error("请求处理异常 - RequestId: {}, Path: {}, Error: {}", 
                        requestId, path, throwable.getMessage());
                });
        };
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 记录请求开始
     */
    private void logRequestStart(String requestId, String tenantId, String serviceName, 
                               String method, String path, String query, String clientIp, String userAgent) {
        if (log.isInfoEnabled()) {
            log.info("请求开始 - RequestId: {}, Tenant: {}, Service: {}, Method: {}, Path: {}, Query: {}, IP: {}, UA: {}", 
                requestId, tenantId, serviceName, method, path, query, clientIp, 
                userAgent != null ? userAgent.substring(0, Math.min(userAgent.length(), 100)) : "");
        }
    }

    /**
     * 记录请求结束
     */
    private void logRequestEnd(String requestId, String tenantId, String serviceName, 
                             int statusCode, long duration, boolean success) {
        String status = success ? "SUCCESS" : "FAILED";
        log.info("请求结束 - RequestId: {}, Tenant: {}, Service: {}, Status: {}, Code: {}, Duration: {}ms", 
            requestId, tenantId, serviceName, status, statusCode, duration);
    }

    /**
     * 记录性能指标
     */
    private void recordMetrics(String tenantId, String serviceName, String method, 
                             int statusCode, long duration, Config config) {
        try {
            String dateKey = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH"));
            
            // 记录请求总数
            String totalKey = String.format("metrics:total:%s:%s:%s", tenantId, serviceName, dateKey);
            redisTemplate.opsForValue().increment(totalKey);
            redisTemplate.expire(totalKey, config.getMetricsRetentionHours(), TimeUnit.HOURS);

            // 记录成功/失败数
            String resultKey = String.format("metrics:%s:%s:%s:%s", 
                statusCode < 400 ? "success" : "error", tenantId, serviceName, dateKey);
            redisTemplate.opsForValue().increment(resultKey);
            redisTemplate.expire(resultKey, config.getMetricsRetentionHours(), TimeUnit.HOURS);

            // 记录响应时间
            String durationKey = String.format("metrics:duration:%s:%s:%s", tenantId, serviceName, dateKey);
            redisTemplate.opsForList().rightPush(durationKey, duration);
            redisTemplate.expire(durationKey, config.getMetricsRetentionHours(), TimeUnit.HOURS);

            // 记录HTTP方法统计
            String methodKey = String.format("metrics:method:%s:%s:%s:%s", method, tenantId, serviceName, dateKey);
            redisTemplate.opsForValue().increment(methodKey);
            redisTemplate.expire(methodKey, config.getMetricsRetentionHours(), TimeUnit.HOURS);

            // 记录状态码统计
            String statusKey = String.format("metrics:status:%d:%s:%s:%s", statusCode, tenantId, serviceName, dateKey);
            redisTemplate.opsForValue().increment(statusKey);
            redisTemplate.expire(statusKey, config.getMetricsRetentionHours(), TimeUnit.HOURS);

            // 记录慢请求
            if (duration > config.getSlowRequestThreshold()) {
                String slowKey = String.format("metrics:slow:%s:%s:%s", tenantId, serviceName, dateKey);
                redisTemplate.opsForValue().increment(slowKey);
                redisTemplate.expire(slowKey, config.getMetricsRetentionHours(), TimeUnit.HOURS);
                
                log.warn("慢请求检测 - Tenant: {}, Service: {}, Duration: {}ms", tenantId, serviceName, duration);
            }

        } catch (Exception e) {
            log.error("记录性能指标失败", e);
        }
    }

    /**
     * 获取租户ID
     */
    private String getTenantId(ServerWebExchange exchange) {
        String tenantId = exchange.getRequest().getHeaders().getFirst("X-Tenant-Id");
        if (tenantId != null) {
            return tenantId;
        }
        
        String path = exchange.getRequest().getURI().getPath();
        if (path.matches("^/(\\d+)/.*")) {
            return path.split("/")[1];
        }
        
        return "unknown";
    }

    /**
     * 获取服务名
     */
    private String getServiceName(ServerWebExchange exchange) {
        String serviceName = exchange.getRequest().getHeaders().getFirst("X-Service-Name");
        if (serviceName != null) {
            return serviceName;
        }
        
        String path = exchange.getRequest().getURI().getPath();
        if (path.matches("^/\\d+/([a-zA-Z-]+)/.*")) {
            return path.split("/")[2];
        }
        
        return "unknown";
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerWebExchange exchange) {
        String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = exchange.getRequest().getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return exchange.getRequest().getRemoteAddress() != null ? 
            exchange.getRequest().getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }



    /**
     * 配置类
     */
    @Data
    public static class Config {
        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 慢请求阈值（毫秒）
         */
        private long slowRequestThreshold = 3000;

        /**
         * 指标数据保留时间（小时）
         */
        private int metricsRetentionHours = 24;

        /**
         * 是否记录请求体（谨慎使用，可能影响性能）
         */
        private boolean logRequestBody = false;

        /**
         * 是否记录响应体（谨慎使用，可能影响性能）
         */
        private boolean logResponseBody = false;
    }
}
