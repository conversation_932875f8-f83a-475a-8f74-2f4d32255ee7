package com.wit.gateway.filter;

import com.wit.gateway.util.GatewayJwtUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

/**
 * JWT认证网关过滤器
 * 适用于 Spring Cloud Gateway 的响应式架构
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtAuthGatewayFilter implements GlobalFilter, Ordered {

    @Autowired
    private GatewayJwtUtil jwtUtil;

    /**
     * 白名单路径（不需要认证）
     */
    private static final List<String> WHITE_LIST_PATHS = Arrays.asList(
        "/auth/login",
        "/auth/register", 
        "/auth/refresh",
        "/auth/captcha",
        "/product/list",
        "/product/detail",
        "/search",
        "/health",
        "/actuator",
        "/doc.html",
        "/swagger",
        "/v3/api-docs",
        "/favicon.ico"
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        
        String path = request.getURI().getPath();
        String method = request.getMethod().name();
        
        log.debug("JWT网关过滤器处理请求: {} {}", method, path);

        // 检查是否在白名单中
        if (isWhiteListPath(path)) {
            log.debug("白名单路径，跳过认证: {}", path);
            return chain.filter(exchange);
        }

        // OPTIONS请求直接放行
        if ("OPTIONS".equals(method)) {
            return chain.filter(exchange);
        }

        // 获取token
        String token = extractToken(request);
        if (!StringUtils.hasText(token)) {
            log.warn("缺少认证token: {}", path);
            return handleAuthError(response, "缺少认证token", HttpStatus.UNAUTHORIZED);
        }

        try {
            // 验证token
            if (!jwtUtil.validateToken(token)) {
                log.warn("token验证失败: {}", path);
                return handleAuthError(response, "token无效或已过期", HttpStatus.UNAUTHORIZED);
            }

            // 解析token并设置用户信息到请求头
            Claims claims = jwtUtil.parseToken(token);
            String userId = claims.getSubject();
            String username = claims.get("username", String.class);
            String tenantId = claims.get("tenantId", String.class);
            @SuppressWarnings("unchecked")
            List<String> roles = claims.get("roles", List.class);

            // 构建新的请求，添加用户信息到请求头
            ServerHttpRequest newRequest = request.mutate()
                .header("X-User-Id", userId)
                .header("X-Username", username)
                .header("X-Tenant-Id", tenantId)
                .header("X-User-Roles", roles != null ? String.join(",", roles) : "")
                .build();

            log.debug("token验证成功: userId={}, tenantId={}", userId, tenantId);
            
            // 继续处理请求
            return chain.filter(exchange.mutate().request(newRequest).build());

        } catch (Exception e) {
            log.error("token验证过程中发生错误: {}", e.getMessage());
            return handleAuthError(response, "认证失败", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 检查是否为白名单路径
     */
    private boolean isWhiteListPath(String path) {
        return WHITE_LIST_PATHS.stream().anyMatch(whitePath -> {
            if (whitePath.endsWith("/**")) {
                String prefix = whitePath.substring(0, whitePath.length() - 3);
                return path.startsWith(prefix);
            } else if (whitePath.endsWith("/*")) {
                String prefix = whitePath.substring(0, whitePath.length() - 2);
                return path.startsWith(prefix) && path.indexOf('/', prefix.length()) == -1;
            } else {
                return path.equals(whitePath) || path.contains(whitePath);
            }
        });
    }

    /**
     * 从请求中提取token
     */
    private String extractToken(ServerHttpRequest request) {
        // 1. 从Authorization头获取
        List<String> authHeaders = request.getHeaders().get("Authorization");
        if (authHeaders != null && !authHeaders.isEmpty()) {
            String authHeader = authHeaders.get(0);
            if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
                return authHeader.substring(7);
            }
        }

        // 2. 从请求参数获取
        List<String> tokenParams = request.getQueryParams().get("token");
        if (tokenParams != null && !tokenParams.isEmpty()) {
            return tokenParams.get(0);
        }

        // 3. 从X-Token头获取
        List<String> xTokenHeaders = request.getHeaders().get("X-Token");
        if (xTokenHeaders != null && !xTokenHeaders.isEmpty()) {
            return xTokenHeaders.get(0);
        }

        return null;
    }

    /**
     * 处理认证错误
     */
    private Mono<Void> handleAuthError(ServerHttpResponse response, String message, HttpStatus status) {
        response.setStatusCode(status);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        response.getHeaders().add("Access-Control-Allow-Origin", "*");
        response.getHeaders().add("Access-Control-Allow-Methods", "*");
        response.getHeaders().add("Access-Control-Allow-Headers", "*");
        
        String errorResponse = String.format(
            "{\"code\":%d,\"message\":\"%s\",\"timestamp\":%d}",
            status.value(), message, System.currentTimeMillis()
        );
        
        return response.writeWith(Mono.just(response.bufferFactory().wrap(errorResponse.getBytes())));
    }

    @Override
    public int getOrder() {
        return -100; // 优先级高，早于其他过滤器执行
    }
}
