@echo off
chcp 65001 >nul

echo === Wit Mall Gateway 启动脚本 ===

REM 设置环境变量
set SPRING_PROFILES_ACTIVE=prod
set NACOS_SERVER_ADDR=***************:8848
set NACOS_USERNAME=nacos
set NACOS_PASSWORD=nacos
set REDIS_HOST=***************
set REDIS_PORT=6379

REM 可选：设置 Redis 密码
REM set REDIS_PASSWORD=your_redis_password

REM 可选：设置 JWT 密钥
REM set JWT_SECRET=your_jwt_secret

echo 环境配置：
echo   - Profile: %SPRING_PROFILES_ACTIVE%
echo   - Nacos: %NACOS_SERVER_ADDR% (User: %NACOS_USERNAME%)
echo   - Redis: %REDIS_HOST%:%REDIS_PORT%

REM 检查 Java 环境
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到 Java 环境，请先安装 Java 8 或更高版本
    pause
    exit /b 1
)

echo Java 版本：
java -version

REM 检查 Maven 是否已构建
if not exist "target\wit-gateway-1.0.0.jar" (
    echo 未找到构建文件，开始构建...
    mvn clean package -DskipTests
    if errorlevel 1 (
        echo 错误：构建失败
        pause
        exit /b 1
    )
)

echo 启动网关服务...

REM 启动应用
set JAVA_OPTS=-Xms512m -Xmx1024m -Dspring.profiles.active=%SPRING_PROFILES_ACTIVE%

java %JAVA_OPTS% -jar target\wit-gateway-1.0.0.jar

echo 网关服务已启动
pause
