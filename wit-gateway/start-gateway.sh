#!/bin/bash

# Wit Mall Gateway 启动脚本
# 生产环境启动脚本

echo "=== Wit Mall Gateway 启动脚本 ==="

# 设置环境变量
export SPRING_PROFILES_ACTIVE=prod
export NACOS_SERVER_ADDR=***************:8848
export NACOS_USERNAME=nacos
export NACOS_PASSWORD=nacos
export REDIS_HOST=***************
export REDIS_PORT=6379

# 可选：设置 Redis 密码
# export REDIS_PASSWORD=your_redis_password

# 可选：设置 JWT 密钥
# export JWT_SECRET=your_jwt_secret

echo "环境配置："
echo "  - Profile: $SPRING_PROFILES_ACTIVE"
echo "  - Nacos: $NACOS_SERVER_ADDR"
echo "  - Redis: $REDIS_HOST:$REDIS_PORT"

# 检查 Java 环境
if ! command -v java &> /dev/null; then
    echo "错误：未找到 Java 环境，请先安装 Java 8 或更高版本"
    exit 1
fi

echo "Java 版本："
java -version

# 检查 Maven 是否已构建
if [ ! -f "target/wit-gateway-1.0.0.jar" ]; then
    echo "未找到构建文件，开始构建..."
    if command -v mvn &> /dev/null; then
        mvn clean package -DskipTests
    else
        echo "错误：未找到 Maven，请先构建项目"
        exit 1
    fi
fi

echo "启动网关服务..."

# 启动应用
java -jar \
    -Xms512m \
    -Xmx1024m \
    -Dspring.profiles.active=$SPRING_PROFILES_ACTIVE \
    -Dnacos.server-addr=$NACOS_SERVER_ADDR \
    -Dredis.host=$REDIS_HOST \
    -Dredis.port=$REDIS_PORT \
    target/wit-gateway-1.0.0.jar

echo "网关服务已启动"
