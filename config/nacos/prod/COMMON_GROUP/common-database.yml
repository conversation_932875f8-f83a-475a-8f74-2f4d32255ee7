# 公共数据库配置 - 生产环境
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: COMMON_GROUP
# 说明: 所有微服务共享的数据库配置

# 数据源公共配置（支持跨环境部署）
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # Druid连接池公共配置
    druid:
      # 初始连接数
      initial-size: 10
      # 最小空闲连接数
      min-idle: 10
      # 最大活跃连接数
      max-active: 200
      # 获取连接等待超时时间
      max-wait: 60000
      # 间隔多久进行一次检测，检测需要关闭的空闲连接
      time-between-eviction-runs-millis: 60000
      # 一个连接在池中最小生存的时间
      min-evictable-idle-time-millis: 300000
      # 最大生存时间
      max-evictable-idle-time-millis: 900000
      # 用来检测连接是否有效的sql
      validation-query: SELECT 1
      # 建议配置为true，不影响性能，并且保证安全性
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache
      pool-prepared-statements: true
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
      max-pool-prepared-statement-per-connection-size: 20
      
      # 连接属性配置
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
      
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}
        allow: # IP白名单，为空则允许所有
        deny:  # IP黑名单
      
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*,/actuator/*"
        session-stat-enable: true
        session-stat-max-count: 1000
        principal-session-name: session_id
        principal-cookie-name: cookie_id
        profile-enable: true
      
      # SQL监控配置
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          slow-sql-millis: 3000
          log-slow-sql: true
          merge-sql: true
        
        # SQL防火墙
        wall:
          enabled: true
          config:
            # 是否允许执行多条SQL
            multi-statement-allow: false
            # 是否允许执行批量SQL
            batch-allow: false
            # 是否允许执行删除语句
            delete-allow: true
            # 是否允许执行更新语句
            update-allow: true
            # 是否允许执行插入语句
            insert-allow: true
            # 是否允许执行查询语句
            select-allow: true
            # 是否允许执行创建表语句
            create-table-allow: false
            # 是否允许执行删除表语句
            drop-table-allow: false
            # 是否允许执行修改表语句
            alter-table-allow: false
            # 是否允许执行截断表语句
            truncate-allow: false

# MyBatis Plus公共配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 关闭二级缓存
    cache-enabled: false
    # 打印SQL语句（生产环境建议关闭）
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 设置超时时间
    default-statement-timeout: 30
    # 设置获取数据库自增主键值
    use-generated-keys: true
    # 配置默认的执行器
    default-executor-type: REUSE
    # 指定 MyBatis 所用日志的具体实现
    local-cache-scope: SESSION
    # 当设置为 true 的时候，懒加载的对象可能被任何懒属性全部加载
    aggressive-lazy-loading: false
    # 允许或不允许多种结果集从一个单独的语句中返回
    multiple-result-sets-enabled: true
    # 使用列标签代替列名
    use-column-label: true
    # 允许 JDBC 支持生成的键
    auto-mapping-behavior: PARTIAL
    # 配置默认的未知列处理策略
    auto-mapping-unknown-column-behavior: WARNING
    # 配置数据库超时时间
    call-setters-on-nulls: false
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    return-instance-for-empty-row: false
    # 指定动态SQL生成的默认语言
    default-scripting-language: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
    # 指定枚举使用的默认 TypeHandler
    default-enum-type-handler: org.apache.ibatis.type.EnumTypeHandler

  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型（AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID"）
      id-type: ASSIGN_ID
      # 字段策略（IGNORED:"忽略判断", NOT_NULL:"非 NULL 判断", NOT_EMPTY:"非空判断"）
      field-strategy: NOT_EMPTY
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 数据库类型
      db-type: MYSQL
      # 表名前缀
      table-prefix: 
      # 实体扫描，多个package用逗号或者分号分隔
      type-aliases-package: com.wit.*.entity
      # 是否开启大写命名
      capital-mode: false
      # 是否开启 kotlin 模式
      kotlin-mode: false
      # 标识符引用符号
      identifier-quote: 
      # 表名是否使用驼峰转下划线命名
      table-underline: true
      # 列名是否使用驼峰转下划线命名
      column-underline: true
      # 插入策略
      insert-strategy: NOT_NULL
      # 更新策略
      update-strategy: NOT_NULL
      # 查询策略
      select-strategy: NOT_EMPTY

    # 是否控制台 print mybatis-plus 的 LOGO
    banner: false
    # 是否开启 SQL 解析缓存
    sql-parser-cache: true

# 分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
  page-size-zero: true
  offset-as-page-num: false
  row-bounds-with-count: false
  auto-runtime-dialect: false
  close-conn: true
  default-count: true

# 数据库连接池监控配置
wit:
  datasource:
    # 是否启用数据源监控
    monitor:
      enabled: true
      # 慢查询阈值（毫秒）
      slow-query-threshold: 3000
      # 是否记录慢查询日志
      log-slow-query: true
      # 连接池监控间隔（秒）
      monitor-interval: 60
      # 是否启用连接泄漏检测
      leak-detection: true
      # 连接泄漏检测阈值（毫秒）
      leak-detection-threshold: 60000

    # 数据库健康检查
    health-check:
      enabled: true
      # 检查间隔（秒）
      check-interval: 30
      # 检查超时时间（毫秒）
      check-timeout: 5000
      # 失败重试次数
      retry-count: 3

    # 数据库备份配置
    backup:
      enabled: false
      # 备份路径
      backup-path: /data/backup/mysql
      # 备份保留天数
      retention-days: 7
      # 备份时间（cron表达式）
      cron: "0 0 2 * * ?"
