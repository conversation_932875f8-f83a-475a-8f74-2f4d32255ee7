# 公共Redis配置 - 生产环境
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: COMMON_GROUP
# 说明: 所有微服务共享的Redis配置

spring:
  redis:
    # Redis服务器地址
    host: ${REDIS_HOST:wit-redis}
    # Redis服务器端口
    port: ${REDIS_PORT:6379}
    # Redis服务器密码（生产环境建议设置）
    password: ${REDIS_PASSWORD:}
    # 连接超时时间
    timeout: 3000ms
    # 数据库索引（默认为0）
    database: 0
    # Spring Cache配置
    cache:
      type: redis
      redis:
        # 缓存过期时间（毫秒）
        time-to-live: 3600000
        # 是否缓存空值
        cache-null-values: false
        # 键前缀
        key-prefix: "wit:cache:"
        # 是否使用键前缀
        use-key-prefix: true
    
    # Lettuce连接池配置（推荐使用Lettuce）
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 3000ms
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 5
        # 空闲连接检测间隔时间
        time-between-eviction-runs: 30s
      # 关闭超时时间
      shutdown-timeout: 100ms
      
    # Jedis连接池配置（备选方案）
    jedis:
      pool:
        max-active: 200
        max-wait: 3000ms
        max-idle: 20
        min-idle: 5
        time-between-eviction-runs: 30s

    # 集群配置（如果使用Redis集群）
    cluster:
      # 集群节点
      nodes: 
        # - wit-redis-1:6379
        # - wit-redis-2:6379
        # - wit-redis-3:6379
      # 最大重定向次数
      max-redirects: 3
      
    # 哨兵配置（如果使用Redis哨兵）
    sentinel:
      # 主节点名称
      master: mymaster
      # 哨兵节点
      nodes:
        # - wit-redis-sentinel-1:26379
        # - wit-redis-sentinel-2:26379
        # - wit-redis-sentinel-3:26379
      # 哨兵密码
      password: ${REDIS_SENTINEL_PASSWORD:}

# Redis缓存配置
cache:
  redis:
    # 默认过期时间（秒）
    default-expiration: 3600
    # 缓存名称配置
    cache-names:
      - userCache
      - productCache
      - orderCache
      - cartCache
      - configCache
    # 缓存过期时间配置（秒）
    expires:
      userCache: 1800      # 用户缓存30分钟
      productCache: 3600   # 商品缓存1小时
      orderCache: 1800     # 订单缓存30分钟
      cartCache: 7200      # 购物车缓存2小时
      configCache: 86400   # 配置缓存24小时
    
    # 缓存键前缀
    key-prefix: "wit:mall:"
    
    # 是否使用缓存前缀
    use-key-prefix: true
    
    # 是否缓存空值
    cache-null-values: false
    
    # 序列化配置
    serialization:
      # 键序列化方式
      key-serializer: string
      # 值序列化方式
      value-serializer: json
      # 哈希键序列化方式
      hash-key-serializer: string
      # 哈希值序列化方式
      hash-value-serializer: json

# Redis分布式锁配置
wit:
  redis:
    # 分布式锁配置
    lock:
      # 是否启用分布式锁
      enabled: true
      # 锁前缀
      key-prefix: "wit:lock:"
      # 默认锁过期时间（毫秒）
      default-expire-time: 30000
      # 默认等待时间（毫秒）
      default-wait-time: 3000
      # 重试间隔（毫秒）
      retry-interval: 100
      # 看门狗续期时间（毫秒）
      watchdog-timeout: 30000

    # 限流配置
    rate-limit:
      # 是否启用限流
      enabled: true
      # 限流键前缀
      key-prefix: "wit:rate_limit:"
      # 默认限流规则
      default-rules:
        # 每分钟最大请求数
        requests-per-minute: 1000
        # 每小时最大请求数
        requests-per-hour: 10000
        # 每天最大请求数
        requests-per-day: 100000

    # 缓存配置
    cache:
      # 缓存键前缀
      key-prefix: "wit:cache:"
      # 默认过期时间（秒）
      default-ttl: 3600
      # 空值缓存时间（秒）
      null-value-ttl: 300
      # 是否启用缓存统计
      enable-statistics: true
      # 缓存预热配置
      warm-up:
        enabled: false
        # 预热数据源
        data-sources: []

    # 会话配置
    session:
      # 会话键前缀
      key-prefix: "wit:session:"
      # 会话过期时间（秒）
      timeout: 7200
      # 是否启用会话共享
      enable-sharing: true

    # 消息队列配置
    mq:
      # 是否启用Redis消息队列
      enabled: true
      # 队列键前缀
      key-prefix: "wit:mq:"
      # 默认队列配置
      default-queue:
        # 队列最大长度
        max-length: 10000
        # 消息过期时间（秒）
        message-ttl: 3600
        # 死信队列
        dead-letter-queue: "wit:mq:dead_letter"

    # 监控配置
    monitor:
      # 是否启用监控
      enabled: true
      # 监控间隔（秒）
      interval: 60
      # 慢查询阈值（毫秒）
      slow-query-threshold: 1000
      # 是否记录慢查询
      log-slow-query: true
      # 连接池监控
      pool-monitor:
        enabled: true
        # 监控指标
        metrics:
          - active-connections
          - idle-connections
          - total-connections
          - wait-count

    # 健康检查配置
    health-check:
      # 是否启用健康检查
      enabled: true
      # 检查间隔（秒）
      interval: 30
      # 检查超时时间（毫秒）
      timeout: 5000
      # 失败重试次数
      retry-count: 3
      # 健康检查命令
      check-command: "PING"

    # 数据备份配置
    backup:
      # 是否启用备份
      enabled: false
      # 备份类型（RDB/AOF）
      type: RDB
      # 备份路径
      backup-path: /data/backup/redis
      # 备份保留天数
      retention-days: 7
      # 备份时间（cron表达式）
      cron: "0 0 3 * * ?"

    # 安全配置
    security:
      # 是否启用安全模式
      enabled: true
      # 允许的命令白名单
      allowed-commands:
        - GET
        - SET
        - DEL
        - EXISTS
        - EXPIRE
        - TTL
        - INCR
        - DECR
        - HGET
        - HSET
        - HDEL
        - LPUSH
        - RPUSH
        - LPOP
        - RPOP
        - SADD
        - SREM
        - SMEMBERS
        - ZADD
        - ZREM
        - ZRANGE
        - ZRANGEBYSCORE
      
      # 禁用的危险命令
      disabled-commands:
        - FLUSHDB
        - FLUSHALL
        - KEYS
        - CONFIG
        - SHUTDOWN
        - DEBUG
        - EVAL
        - SCRIPT
