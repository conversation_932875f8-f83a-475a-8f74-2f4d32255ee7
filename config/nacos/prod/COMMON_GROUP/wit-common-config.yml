# WitMall Common服务专用配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: COMMON_GROUP
# 说明: Common模块的专用配置，包含公共组件的配置

# Common模块配置
wit:
  common:
    # 模块基本信息
    info:
      name: "WitMall公共模块"
      version: "1.0.0"
      description: "提供统一的工具类、配置和基础组件"
      author: "Wit"
      
    # 全局异常处理配置
    exception:
      # 是否启用全局异常处理
      enabled: true
      # 是否打印异常堆栈
      print-stack-trace: true
      # 异常信息国际化
      i18n:
        enabled: false
        default-locale: zh_CN
      # 敏感信息脱敏
      desensitization:
        enabled: true
        # 脱敏字段
        fields:
          - password
          - phone
          - email
          - idCard
          
    # 统一响应配置
    response:
      # 是否启用统一响应包装
      enabled: true
      # 成功响应码
      success-code: 200
      # 默认成功消息
      success-message: "操作成功"
      # 默认失败消息
      error-message: "操作失败"
      # 是否包含时间戳
      include-timestamp: true
      # 是否包含请求路径
      include-path: true
      
    # 参数校验配置
    validation:
      # 是否启用参数校验
      enabled: true
      # 快速失败模式
      fail-fast: true
      # 校验消息国际化
      i18n:
        enabled: false
        
    # 跨域配置
    cors:
      # 是否启用跨域
      enabled: true
      # 允许的源
      allowed-origins: "*"
      # 允许的方法
      allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
      # 允许的头
      allowed-headers: "*"
      # 是否允许凭证
      allow-credentials: false
      # 预检请求缓存时间
      max-age: 3600
      
    # 序列化配置
    serialization:
      # Jackson配置
      jackson:
        # 日期格式
        date-format: "yyyy-MM-dd HH:mm:ss"
        # 时区
        time-zone: "GMT+8"
        # 空值处理
        default-property-inclusion: "NON_NULL"
        # 序列化配置
        serialization:
          write-dates-as-timestamps: false
          fail-on-empty-beans: false
        # 反序列化配置
        deserialization:
          fail-on-unknown-properties: false
          
    # 缓存配置
    cache:
      # 是否启用缓存
      enabled: true
      # 缓存类型
      type: redis
      # 缓存前缀
      key-prefix: "wit:common:"
      # 默认过期时间（秒）
      default-ttl: 3600
      # 空值缓存时间（秒）
      null-value-ttl: 300
      
    # 工具类配置
    utils:
      # 雪花算法配置
      snowflake:
        # 数据中心ID
        datacenter-id: 1
        # 机器ID
        machine-id: 1
        
      # 文件上传配置
      file-upload:
        # 上传路径
        upload-path: "/data/uploads"
        # 允许的文件类型
        allowed-types: "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx"
        # 最大文件大小（MB）
        max-size: 10
        
      # 验证码配置
      captcha:
        # 验证码长度
        length: 4
        # 验证码类型（数字、字母、混合）
        type: "mixed"
        # 过期时间（秒）
        expire-time: 300
        
    # 监控配置
    monitor:
      # 是否启用监控
      enabled: true
      # 性能监控
      performance:
        enabled: true
        # 慢方法阈值（毫秒）
        slow-method-threshold: 1000
      # 内存监控
      memory:
        enabled: true
        # 内存使用率告警阈值
        warning-threshold: 80
        
    # 安全配置
    security:
      # XSS防护
      xss:
        enabled: true
        # 过滤的URL模式
        url-patterns: "/*"
        # 排除的URL
        exclude-urls: 
          - "/actuator/**"
          - "/swagger-ui/**"
          - "/v3/api-docs/**"
      # SQL注入防护
      sql-injection:
        enabled: true
        # 敏感关键词
        keywords:
          - "select"
          - "insert"
          - "update"
          - "delete"
          - "drop"
          - "truncate"
          
    # 线程池配置
    thread-pool:
      # 核心线程数
      core-pool-size: 10
      # 最大线程数
      max-pool-size: 50
      # 队列容量
      queue-capacity: 1000
      # 线程空闲时间（秒）
      keep-alive-seconds: 60
      # 线程名前缀
      thread-name-prefix: "wit-common-"
      
    # 重试配置
    retry:
      # 是否启用重试
      enabled: true
      # 最大重试次数
      max-attempts: 3
      # 重试间隔（毫秒）
      delay: 1000
      # 重试倍数
      multiplier: 2
      
    # 限流配置
    rate-limit:
      # 是否启用限流
      enabled: true
      # 默认限流规则
      default-rules:
        # 每秒最大请求数
        requests-per-second: 100
        # 每分钟最大请求数
        requests-per-minute: 1000
        # 每小时最大请求数
        requests-per-hour: 10000

# Spring配置增强
spring:
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
      
  # 任务调度配置
  task:
    scheduling:
      pool:
        size: 10
    execution:
      pool:
        core-size: 10
        max-size: 50
        queue-capacity: 1000
        keep-alive: 60s
        thread-name-prefix: "wit-task-"

# 服务器配置
server:
  # 编码配置
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # Tomcat配置
  tomcat:
    # 最大连接数
    max-connections: 8192
    # 最大线程数
    threads:
      max: 200
      min-spare: 10
    # 连接超时时间
    connection-timeout: 20000ms
    # 最大HTTP头大小
    max-http-header-size: 8192
