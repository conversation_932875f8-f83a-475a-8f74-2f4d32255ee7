# ===================================================================
# Wit Mall 公共配置文件
# ===================================================================
#
# 配置说明：
# - Data ID: common-config.yml
# - Group: DEFAULT_GROUP
# - 用途: 所有微服务共享的通用配置
#
# 配置模块：
# 1. 监控配置 - Spring Boot Actuator监控端点
# 2. 日志配置 - 统一的日志级别和格式
# 3. API文档配置 - Swagger/Knife4j文档生成
# 4. 数据库配置 - MyBatis Plus通用配置
# 5. 线程池配置 - 异步任务和定时任务线程池
# ===================================================================

# ===================================================================
# 监控配置 - Spring Boot Actuator
# ===================================================================
management:
  endpoints:
    web:
      exposure:
        include: '*'                            # 暴露所有监控端点
  endpoint:
    health:
      show-details: always                      # 显示详细的健康检查信息

# ===================================================================
# 日志配置 - 统一的日志级别和格式
# ===================================================================
logging:
  level:
    root: INFO                                  # 根日志级别
    com.wit: DEBUG                              # 项目包日志级别（开发环境）
    org.springframework.cloud: DEBUG           # Spring Cloud日志级别
  pattern:
    # 控制台日志格式：时间 [线程] 级别 [类名] - 消息
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n'
    # 文件日志格式：与控制台格式相同
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n'

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.wit
  paths-to-match: /**

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: *************:8001
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Apache License 2.0 | Copyright  2023-[Wit Mall](https://github.com/wit-mall)

# 分页配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 线程池配置
spring:
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: wit-task-
    scheduling:
      pool:
        size: 4
      thread-name-prefix: wit-scheduling-
