# Wit Mall 统一JWT配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: DEFAULT_GROUP

# 统一JWT配置
wit:
  jwt:
    # JWT密钥（生产环境请使用环境变量）
    secret: ${JWT_SECRET:wit-mall-unified-secret-key-2024-very-long-secret-for-production-environment}
    
    # JWT过期时间（秒）
    expiration: 7200  # 2小时
    
    # 刷新token过期时间（秒）
    refresh-expiration: 604800  # 7天
    
    # JWT签发者
    issuer: wit-mall
    
    # JWT算法
    algorithm: HS256
    
    # 是否在响应头中返回token
    response-header: true
    
    # token请求头名称
    header-name: Authorization
    
    # token前缀
    token-prefix: "Bearer "
    
    # 租户头名称
    tenant-header-name: X-Tenant-Id

# 认证配置
auth:
  # 白名单路径（不需要认证）
  exclude-paths:
    - /*/auth/login
    - /*/auth/register
    - /*/auth/refresh
    - /*/auth/captcha
    - /*/product/list
    - /*/product/detail/**
    - /*/search/**
    - /health
    - /actuator/health
    - /actuator/info
    - /gateway/health
    - /gateway/info
    - /gateway/tenants
    - /gateway/test/**
    
  # 认证头配置
  header:
    token-name: Authorization
    token-prefix: "Bearer "
    tenant-name: X-Tenant-Id
    user-id-name: X-User-Id
    username-name: X-Username
    user-roles-name: X-User-Roles

# 安全配置
security:
  # 密码配置
  password:
    # 密码加密强度
    strength: 12
    # 密码最小长度
    min-length: 8
    # 密码最大长度
    max-length: 20
    # 是否需要包含特殊字符
    require-special-char: true
    # 密码过期天数
    expire-days: 90

  # 登录限制
  login:
    # 最大失败次数
    max-fail-count: 5
    # 锁定时间（分钟）
    lock-time: 30
    # 验证码触发失败次数
    captcha-trigger-count: 3

  # 会话配置
  session:
    # 是否允许多地登录
    allow-multiple-login: false
    # 会话超时时间（分钟）
    timeout: 120
    # 踢出之前登录的用户
    kick-out-previous: true

# JWT增强安全配置（电商专用）
jwt:
  security:
    # 是否启用增强安全模式
    enhanced-mode: true

    # Token安全配置
    token:
      # 是否启用Token刷新机制
      refresh-enabled: true
      # Token自动刷新阈值（剩余时间少于此值时自动刷新，秒）
      auto-refresh-threshold: 1800  # 30分钟
      # 是否启用Token黑名单
      blacklist-enabled: true
      # 黑名单缓存时间（秒）
      blacklist-cache-time: 7200

    # 用户信息加密
    user-info:
      # 是否加密敏感信息
      encrypt-sensitive: true
      # 敏感字段列表
      sensitive-fields:
        - phone
        - email
        - balance
        - points
      # 加密算法
      encrypt-algorithm: AES
      # 加密密钥（生产环境使用环境变量）
      encrypt-key: ${JWT_ENCRYPT_KEY:wit-mall-encrypt-key-2024}

    # IP限制
    ip-restriction:
      # 是否启用IP限制
      enabled: true
      # 是否检查IP变化
      check-ip-change: true
      # IP变化时的处理方式（LOGOUT: 强制退出, WARNING: 仅警告）
      ip-change-action: WARNING
      # IP白名单
      whitelist:
        - "192.168.*.*"
        - "10.*.*.*"
        - "172.16.*.*"

    # 设备限制
    device-restriction:
      # 是否启用设备限制
      enabled: true
      # 同一用户最大设备数
      max-devices: 3
      # 设备类型限制
      allowed-device-types:
        - WEB
        - MOBILE
        - APP
        - TABLET
      # 新设备登录时的处理方式
      new-device-action: REQUIRE_VERIFICATION

    # 权限验证
    permission:
      # 是否启用细粒度权限验证
      fine-grained: true
      # 权限缓存时间（秒）
      cache-time: 1800
      # 是否启用权限继承
      inheritance-enabled: true

    # 审计日志
    audit:
      # 是否启用审计日志
      enabled: true
      # 记录的操作类型
      logged-operations:
        - LOGIN
        - LOGOUT
        - TOKEN_REFRESH
        - PERMISSION_CHECK
        - SENSITIVE_OPERATION
      # 日志保留天数
      retention-days: 90

    # 风险控制
    risk-control:
      # 是否启用风险控制
      enabled: true
      # 异常登录检测
      anomaly-detection:
        # 是否启用异常地理位置检测
        geo-location: true
        # 是否启用异常时间检测
        unusual-time: true
        # 是否启用异常设备检测
        unusual-device: true
      # 风险等级阈值
      risk-threshold:
        low: 30
        medium: 60
        high: 80
      # 高风险操作需要额外验证
      high-risk-verification: true

# 电商业务安全配置
ecommerce:
  security:
    # 支付相关安全
    payment:
      # 支付操作需要二次验证
      require-second-verification: true
      # 支付Token有效期（秒）
      payment-token-expire: 300  # 5分钟
      # 大额支付阈值（元）
      large-amount-threshold: 1000

    # 订单相关安全
    order:
      # 订单操作需要验证用户身份
      require-user-verification: true
      # 批量操作限制
      batch-operation-limit: 10

    # 商品相关安全
    product:
      # 商品价格修改需要特殊权限
      price-change-permission: "PRODUCT:PRICE:UPDATE"
      # 库存修改需要特殊权限
      stock-change-permission: "PRODUCT:STOCK:UPDATE"

    # 用户相关安全
    user:
      # 用户信息修改需要验证
      profile-change-verification: true
      # 敏感信息查看权限
      sensitive-info-permission: "USER:SENSITIVE:VIEW"
      # VIP信息修改权限
      vip-change-permission: "USER:VIP:UPDATE"
