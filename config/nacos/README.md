# Wit Mall Nacos 配置管理中心

## 📋 配置概述

Nacos配置管理是Wit Mall微服务架构的核心组件，负责统一管理所有微服务的配置信息。通过配置中心，我们可以实现：

- **集中化配置管理**：所有服务配置统一存储和管理
- **动态配置更新**：支持配置热更新，无需重启服务
- **环境隔离**：支持多环境配置（dev/test/prod）
- **配置分组**：按功能模块分组管理配置
- **配置共享**：公共配置可被多个服务共享
- **配置版本管理**：支持配置历史版本和回滚

## 🏗️ 目录结构

```
config/nacos/
├── prod/                           # 🏭 生产环境配置
│   ├── DEFAULT_GROUP/              # 🔧 基础服务组
│   │   ├── wit-gateway-core.yml    # 网关核心配置（端口、监控等）
│   │   ├── wit-gateway-routes.yml  # 网关路由配置（服务路由规则）
│   │   └── wit-gateway-security.yml # 网关安全配置（认证、授权）
│   ├── BUSINESS_GROUP/             # 💼 业务服务组
│   │   ├── wit-user-core.yml       # 用户服务核心配置
│   │   ├── wit-user-database.yml   # 用户服务数据库配置
│   │   ├── wit-user-redis.yml      # 用户服务Redis配置
│   │   └── wit-user-security.yml   # 用户服务安全配置
│   └── COMMON_GROUP/               # 🔗 公共配置组
│       ├── common-database.yml     # 公共数据库配置（连接池、事务等）
│       ├── common-redis.yml        # 公共Redis配置（缓存、会话等）
│       ├── common-logging.yml      # 公共日志配置（级别、格式等）
│       └── common-mq.yml           # 公共消息队列配置（RabbitMQ等）
├── test/                           # 🧪 测试环境配置（待创建）
├── dev/                            # 🔨 开发环境配置（待创建）
├── deploy-configs.sh               # 📦 配置部署脚本（自动化部署）
├── common-config.yml               # 🌐 通用配置模板
├── dubbo-config.yml                # 🚀 Dubbo RPC配置模板
└── README.md                       # 📖 配置说明文档
```

## 🎯 命名空间设计

### 环境命名空间
- **wit** (1f290fcd-60d0-48d1-893c-0d2ffb30625d) - 生产环境
- **test** - 测试环境（待创建）
- **dev** - 开发环境（待创建）

### 服务分组设计
- **DEFAULT_GROUP** - 基础服务组（网关、认证等）
- **BUSINESS_GROUP** - 业务服务组（用户、商品、订单等）
- **SUPPORT_GROUP** - 支撑服务组（文件、通知、定时任务等）
- **SYSTEM_GROUP** - 系统服务组（监控、日志等）
- **COMMON_GROUP** - 公共配置组（数据库、Redis等共享配置）

## 📋 配置文件说明

### 1. 网关服务配置
#### wit-gateway-core.yml
- **位置**: `prod/DEFAULT_GROUP/wit-gateway-core.yml`
- **功能**: 网关核心配置、基础设置、监控配置

#### wit-gateway-routes.yml
- **位置**: `prod/DEFAULT_GROUP/wit-gateway-routes.yml`
- **功能**: 路由规则、负载均衡、动态路由配置

#### wit-gateway-security.yml
- **位置**: `prod/DEFAULT_GROUP/wit-gateway-security.yml`
- **功能**: 认证授权、限流熔断、安全防护配置

### 2. 用户服务配置
#### wit-user-core.yml
- **位置**: `prod/BUSINESS_GROUP/wit-user-core.yml`
- **功能**: 用户服务核心业务配置、验证码、第三方登录

#### wit-user-database.yml
- **位置**: `prod/BUSINESS_GROUP/wit-user-database.yml`
- **功能**: 数据库连接、MyBatis Plus、数据备份归档

#### wit-user-redis.yml
- **位置**: `prod/BUSINESS_GROUP/wit-user-redis.yml`
- **功能**: Redis缓存、分布式锁、限流、会话管理

#### wit-user-security.yml
- **位置**: `prod/BUSINESS_GROUP/wit-user-security.yml`
- **功能**: JWT配置、密码策略、权限控制、安全防护

### 3. 公共配置
#### common-database.yml
- **位置**: `prod/COMMON_GROUP/common-database.yml`
- **功能**: Druid连接池、MyBatis Plus、数据库监控

#### common-redis.yml
- **位置**: `prod/COMMON_GROUP/common-redis.yml`
- **功能**: Redis连接、缓存策略、分布式锁、监控

#### common-logging.yml
- **位置**: `prod/COMMON_GROUP/common-logging.yml`
- **功能**: 日志配置、链路追踪、日志脱敏、监控告警

#### common-mq.yml
- **位置**: `prod/COMMON_GROUP/common-mq.yml`
- **功能**: RabbitMQ配置、交换机队列、消息路由、监控

## 🚀 使用方法

### 1. 在Nacos控制台创建配置

1. 登录Nacos控制台: http://localhost:8848/nacos
2. 选择对应的命名空间: `wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)`
3. 在对应的分组中创建配置文件
4. 复制对应的配置内容到Nacos中

### 2. 微服务配置引用

在微服务的 `bootstrap.yml` 或 `application.yml` 中配置：

```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d  # wit命名空间ID
        group: BUSINESS_GROUP  # 对应的服务组
      config:
        server-addr: wit-nacos:8848
        namespace: 1f290fcd-60d0-48d1-893c-0d2ffb30625d
        group: BUSINESS_GROUP
        file-extension: yml
        refresh-enabled: true
        # 引用用户服务的配置文件
        extension-configs:
          - data-id: wit-user-core.yml
            group: BUSINESS_GROUP
            refresh: true
          - data-id: wit-user-database.yml
            group: BUSINESS_GROUP
            refresh: true
          - data-id: wit-user-redis.yml
            group: BUSINESS_GROUP
            refresh: true
          - data-id: wit-user-security.yml
            group: BUSINESS_GROUP
            refresh: true
        # 引用公共配置
        shared-configs:
          - data-id: common-logging.yml
            group: COMMON_GROUP
            refresh: true
          - data-id: common-mq.yml
            group: COMMON_GROUP
            refresh: true
```

### 3. 配置优先级

配置加载优先级（从高到低）：
1. 服务特定配置 (如: wit-user.yml)
2. 共享配置 (如: common-database.yml)
3. 本地配置文件 (application.yml)

## 🔧 配置管理最佳实践

### 1. 环境隔离
- 不同环境使用不同的命名空间
- 生产环境配置严格控制访问权限
- 敏感信息使用环境变量或加密配置

### 2. 配置分层
- 公共配置放在 COMMON_GROUP
- 业务配置按服务类型分组
- 避免配置重复和冲突

### 3. 配置版本管理
- 重要配置变更前先备份
- 使用配置历史功能跟踪变更
- 配置发布采用灰度发布策略

### 4. 监控和告警
- 配置变更监控
- 配置加载失败告警
- 配置中心可用性监控

## 📝 待完成任务

### 1. 其他环境配置
- [ ] 创建测试环境配置
- [ ] 创建开发环境配置

### 2. 其他服务配置
- [ ] wit-auth 认证服务配置
- [ ] wit-product 商品服务配置
- [ ] wit-order 订单服务配置
- [ ] wit-cart 购物车服务配置
- [ ] wit-payment 支付服务配置
- [ ] 其他微服务配置

### 3. 高级配置
- [ ] 配置加密
- [ ] 配置权限控制
- [ ] 配置审计日志
- [ ] 配置自动化部署

## 🔐 安全注意事项

1. **敏感信息处理**
   - 数据库密码使用环境变量
   - JWT密钥使用强随机字符串
   - Redis密码在生产环境必须设置

2. **访问控制**
   - 生产环境Nacos开启认证
   - 配置操作权限严格控制
   - 定期审查配置访问日志

3. **网络安全**
   - Nacos服务端口不对外暴露
   - 使用VPN或内网访问
   - 启用HTTPS传输

## 📞 联系方式

如有配置相关问题，请联系：
- 开发团队: <EMAIL>
- 运维团队: <EMAIL>
