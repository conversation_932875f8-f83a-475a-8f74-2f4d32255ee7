# 公共日志配置 - 生产环境
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: COMMON_GROUP
# 说明: 所有微服务共享的日志配置

# 日志配置
logging:
  # 日志级别配置
  level:
    root: INFO
    # Spring框架日志
    org.springframework: INFO
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.springframework.cloud: INFO
    org.springframework.boot: INFO
    # 数据库相关日志
    org.mybatis: INFO
    com.alibaba.druid: INFO
    # 网络相关日志
    org.apache.http: INFO
    # 缓存相关日志
    org.springframework.cache: INFO
    # 消息队列相关日志
    org.springframework.amqp: INFO
    # Nacos相关日志
    com.alibaba.nacos: INFO
    # 项目日志
    com.wit: INFO
    
  # 日志输出格式
  pattern:
    # 控制台输出格式
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"
    # 文件输出格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"
    
  # 日志文件配置
  file:
    # 日志文件路径
    path: /var/log/wit-mall
    # 日志文件名（会自动添加服务名前缀）
    name: ${spring.application.name}.log
    # 单个日志文件最大大小
    max-size: 100MB
    # 日志文件保留天数
    max-history: 30
    # 日志文件总大小限制
    total-size-cap: 10GB
    
  # Logback配置
  logback:
    rollingpolicy:
      # 日志文件滚动策略
      file-name-pattern: ${LOG_PATH}/${spring.application.name}-%d{yyyy-MM-dd}-%i.log.gz
      # 最大文件大小
      max-file-size: 100MB
      # 保留天数
      max-history: 30
      # 总大小限制
      total-size-cap: 10GB
      # 是否在启动时清理历史日志
      clean-history-on-start: false

# Wit Mall 日志配置
wit:
  logging:
    # 链路追踪配置
    trace:
      enabled: true
      # 追踪ID生成器
      trace-id-generator: uuid
      # 是否在响应头中返回追踪ID
      response-header: true
      # 追踪ID请求头名称
      trace-header-name: X-Trace-Id
      
    # 访问日志配置
    access:
      enabled: true
      # 访问日志格式
      format: "ACCESS: [%X{traceId}] %s %s %s %dms %s %s"
      # 是否记录请求体
      log-request-body: false
      # 是否记录响应体
      log-response-body: false
      # 慢请求阈值（毫秒）
      slow-request-threshold: 3000
      # 排除的URL路径
      exclude-paths:
        - /actuator/**
        - /health
        - /favicon.ico
        - /druid/**
        
    # 错误日志配置
    error:
      enabled: true
      # 是否记录堆栈信息
      include-stack-trace: true
      # 错误日志级别
      level: ERROR
      # 是否发送错误告警
      alert-enabled: true
      
    # 业务日志配置
    business:
      enabled: true
      # 业务日志级别
      level: INFO
      # 业务日志分类
      categories:
        - USER_OPERATION    # 用户操作
        - ORDER_PROCESS     # 订单处理
        - PAYMENT_PROCESS   # 支付处理
        - INVENTORY_CHANGE  # 库存变更
        - SYSTEM_CONFIG     # 系统配置
        
    # 安全日志配置
    security:
      enabled: true
      # 安全日志级别
      level: WARN
      # 安全事件类型
      events:
        - LOGIN_SUCCESS     # 登录成功
        - LOGIN_FAILURE     # 登录失败
        - LOGOUT           # 登出
        - PERMISSION_DENIED # 权限拒绝
        - SUSPICIOUS_ACCESS # 可疑访问
        - DATA_BREACH      # 数据泄露
        
    # 性能日志配置
    performance:
      enabled: true
      # 性能日志级别
      level: INFO
      # 慢查询阈值（毫秒）
      slow-query-threshold: 1000
      # 慢接口阈值（毫秒）
      slow-api-threshold: 3000
      # 是否记录SQL执行时间
      log-sql-time: true
      
    # 日志采样配置
    sampling:
      enabled: false  # 生产环境可开启采样
      # 采样率（0.0-1.0）
      rate: 0.1
      # 采样策略
      strategy: random  # random, fixed, adaptive
      
    # 日志脱敏配置
    desensitization:
      enabled: true
      # 脱敏规则
      rules:
        # 手机号脱敏
        - field: phone
          pattern: "(\\d{3})\\d{4}(\\d{4})"
          replacement: "$1****$2"
        # 身份证脱敏
        - field: idCard
          pattern: "(\\d{6})\\d{8}(\\d{4})"
          replacement: "$1********$2"
        # 邮箱脱敏
        - field: email
          pattern: "(\\w{1,3})\\w*@(\\w+)"
          replacement: "$1***@$2"
        # 银行卡脱敏
        - field: bankCard
          pattern: "(\\d{4})\\d{8,12}(\\d{4})"
          replacement: "$1********$2"
        # 密码脱敏
        - field: password
          pattern: ".*"
          replacement: "******"
          
    # 日志聚合配置
    aggregation:
      enabled: true
      # 聚合间隔（秒）
      interval: 60
      # 聚合维度
      dimensions:
        - service     # 服务维度
        - api        # 接口维度
        - user       # 用户维度
        - error      # 错误维度
        
    # 日志存储配置
    storage:
      # 本地存储配置
      local:
        enabled: true
        path: /var/log/wit-mall
        retention-days: 30
        
      # 远程存储配置（如ELK）
      remote:
        enabled: false
        type: elasticsearch  # elasticsearch, kafka, fluentd
        hosts:
          - elasticsearch:9200
        index-pattern: "wit-mall-logs-%{+YYYY.MM.dd}"
        
    # 日志监控配置
    monitoring:
      enabled: true
      # 监控指标
      metrics:
        - log-count        # 日志数量
        - error-count      # 错误数量
        - warn-count       # 警告数量
        - slow-query-count # 慢查询数量
        - api-response-time # API响应时间
        
      # 告警配置
      alerts:
        # 错误率告警
        - name: error-rate
          condition: "error_count / total_count > 0.05"
          threshold: 0.05
          duration: 5m
          
        # 慢查询告警
        - name: slow-query
          condition: "slow_query_count > 100"
          threshold: 100
          duration: 5m
          
    # 日志清理配置
    cleanup:
      enabled: true
      # 清理策略
      strategies:
        # 按时间清理
        - type: time-based
          retention-days: 30
          cron: "0 0 2 * * ?"  # 每天凌晨2点清理
          
        # 按大小清理
        - type: size-based
          max-total-size: 50GB
          cron: "0 0 3 * * ?"  # 每天凌晨3点检查
          
    # 日志压缩配置
    compression:
      enabled: true
      # 压缩算法
      algorithm: gzip
      # 压缩级别（1-9）
      level: 6
      # 压缩延迟（天）
      delay-days: 1
