# Wit Mall 网关服务 - 安全配置
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: DEFAULT_GROUP

# 网关安全配置
wit:
  gateway:
    # 认证配置
    auth:
      enabled: true
      # 使用统一JWT配置（从wit-jwt-config.yml继承）
      
      # 白名单路径（不需要认证）
      exclude-paths:
        - /api/auth/login
        - /api/auth/register
        - /api/auth/refresh
        - /api/auth/captcha
        - /api/product/list
        - /api/product/detail/**
        - /api/search/**
        - /health
        - /actuator/health
        - /actuator/info
        
      # 认证头配置
      header:
        token-name: Authorization
        token-prefix: "Bearer "
        tenant-name: X-Tenant-Id
        
    # 限流配置
    rate-limit:
      enabled: true
      # Redis键前缀
      redis-key-prefix: "wit:gateway:rate_limit:"
      
      # 默认限流规则
      default-rules:
        # 每秒请求数
        requests-per-second: 100
        # 突发容量
        burst-capacity: 200
        
      # 特殊路径限流规则
      path-rules:
        # 登录接口限流
        "/api/auth/login":
          requests-per-second: 10
          burst-capacity: 20
        # 注册接口限流
        "/api/auth/register":
          requests-per-second: 5
          burst-capacity: 10
        # 支付接口限流
        "/api/payment/**":
          requests-per-second: 20
          burst-capacity: 40
        # 文件上传限流
        "/api/file/upload":
          requests-per-second: 10
          burst-capacity: 20
          
      # IP限流规则
      ip-rules:
        # 单IP每分钟最大请求数
        max-requests-per-minute: 1000
        # 单IP每小时最大请求数
        max-requests-per-hour: 10000
        
    # 熔断器配置
    circuit-breaker:
      enabled: true
      # 失败率阈值
      failure-threshold: 0.6
      # 最小请求数
      minimum-requests: 20
      # 熔断器打开时间窗口（秒）
      sleep-window: 30
      # 请求超时时间（毫秒）
      timeout: 5000
      
      # 服务熔断配置
      services:
        wit-user:
          failure-threshold: 0.5
          minimum-requests: 10
          sleep-window: 30
        wit-product:
          failure-threshold: 0.6
          minimum-requests: 20
          sleep-window: 30
        wit-order:
          failure-threshold: 0.5
          minimum-requests: 15
          sleep-window: 45
        wit-payment:
          failure-threshold: 0.3
          minimum-requests: 5
          sleep-window: 60
          
    # 多租户配置
    tenant:
      validation-enabled: true
      cache-ttl: 3600  # 租户信息缓存时间（秒）
      header-name: X-Tenant-Id
      default-tenant: default
      
      # 租户路由规则
      routing:
        enabled: true
        # 租户路由前缀
        prefix: "/tenant/{tenantId}"
        
    # IP白名单配置
    ip-whitelist:
      enabled: false  # 生产环境根据需要开启
      allowed-ips: []
      # 示例配置
      # allowed-ips:
      #   - ***********/24
      #   - 10.0.0.0/8
      
    # 黑名单配置
    blacklist:
      enabled: true
      # IP黑名单
      blocked-ips: []
      # 用户黑名单
      blocked-users: []
      # 自动封禁配置
      auto-ban:
        enabled: true
        # 5分钟内失败次数超过50次自动封禁
        max-failures: 50
        time-window: 300  # 秒
        ban-duration: 3600  # 封禁时长（秒）
        
    # 请求验证配置
    validation:
      # 请求体大小限制
      max-request-size: 10MB
      # 请求头大小限制
      max-header-size: 8KB
      # URL长度限制
      max-url-length: 2048
      
      # 请求头验证
      headers:
        # 必需的请求头
        required: []
        # 禁止的请求头
        forbidden:
          - X-Forwarded-For
          - X-Real-IP
          
    # 安全头配置
    security-headers:
      enabled: true
      headers:
        # 内容安全策略
        Content-Security-Policy: "default-src 'self'"
        # 防止点击劫持
        X-Frame-Options: "DENY"
        # 防止MIME类型嗅探
        X-Content-Type-Options: "nosniff"
        # XSS保护
        X-XSS-Protection: "1; mode=block"
        # 强制HTTPS
        Strict-Transport-Security: "max-age=31536000; includeSubDomains"
        # 引用策略
        Referrer-Policy: "strict-origin-when-cross-origin"
        
    # 日志配置
    logging:
      # 访问日志
      access-log:
        enabled: true
        format: "combined"
        # 敏感信息脱敏
        desensitization:
          enabled: true
          patterns:
            - "password"
            - "token"
            - "secret"
            
      # 安全日志
      security-log:
        enabled: true
        # 记录的事件类型
        events:
          - AUTHENTICATION_FAILURE
          - AUTHORIZATION_FAILURE
          - RATE_LIMIT_EXCEEDED
          - CIRCUIT_BREAKER_OPEN
          - IP_BLOCKED
          - SUSPICIOUS_REQUEST
          
    # 监控配置
    monitoring:
      # 安全监控
      security:
        enabled: true
        # 异常请求监控
        anomaly-detection:
          enabled: true
          # 异常阈值
          threshold: 0.1
          # 监控窗口（分钟）
          window: 5
          
      # 性能监控
      performance:
        enabled: true
        # 慢请求阈值（毫秒）
        slow-request-threshold: 3000
        # 错误率阈值
        error-rate-threshold: 0.05
