# Wit Mall 网关服务 - 核心配置（本地开发环境适配）
# 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# 分组: DEFAULT_GROUP
# 注意：此配置已适配本地开发环境，支持环境变量覆盖

server:
  port: 8080

spring:
  application:
    name: wit-gateway

  # 注意：Redis配置已移至 common-redis.yml，避免配置冲突
    
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      
      # 全局过滤器配置
      default-filters:
        - name: Retry
          args:
            retries: 3
            methods: GET,POST,PUT,DELETE
            series: SERVER_ERROR
            exceptions: java.io.IOException,java.util.concurrent.TimeoutException
        - name: RequestTime
          args:
            enabled: true
        - name: AddRequestHeader
          args:
            name: X-Gateway-Version
            value: 1.0.0
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 网关核心配置
wit:
  gateway:
    # 基础配置
    basic:
      # 服务名称
      service-name: wit-gateway
      # 服务版本
      version: 1.0.0
      # 服务描述
      description: Wit Mall API Gateway Service
      
    # 监控配置
    monitoring:
      enabled: true
      slow-request-threshold: 3000
      metrics-retention-hours: 24
      alert-enabled: true
      
    # 健康检查配置
    health-check:
      enabled: true
      check-interval: 30
      timeout: 5000
      
    # 请求配置
    request:
      # 请求大小限制
      max-request-size: 10MB
      # 请求头限制
      max-header-size: 8KB
      # 请求超时时间
      timeout: 30000
