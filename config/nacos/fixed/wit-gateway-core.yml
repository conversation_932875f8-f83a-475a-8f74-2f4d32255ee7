# ===================================================================
# Wit Mall 网关服务 - 核心配置文件
# ===================================================================
#
# 配置说明：
# - 命名空间: wit (1f290fcd-60d0-48d1-893c-0d2ffb30625d)
# - 分组: DEFAULT_GROUP
# - 环境: 本地开发环境适配，支持环境变量覆盖
#
# 功能模块：
# 1. 服务端口配置 - 网关服务监听端口
# 2. 服务发现配置 - 自动发现后端微服务
# 3. 全局过滤器配置 - 重试、请求时间、请求头等
# 4. CORS跨域配置 - 支持前端跨域访问
# 5. 监控配置 - 健康检查、指标收集等
# 6. 网关自定义配置 - 业务相关的配置项
#
# 注意事项：
# - 此配置文件为核心配置，包含网关基础功能
# - 路由配置请参考 wit-gateway-routes.yml
# - 安全配置请参考 wit-gateway-security.yml
# ===================================================================

# ===================================================================
# 服务器配置
# ===================================================================
server:
  port: 8080                                    # 网关服务端口

# ===================================================================
# Spring 应用配置
# ===================================================================
spring:
  application:
    name: wit-gateway                           # 服务名称，用于服务注册发现

  # 注意：Redis配置已移至 common-redis.yml，避免配置冲突

  # ===================================================================
  # Spring Cloud Gateway 配置
  # ===================================================================
  cloud:
    gateway:
      # 服务发现配置
      discovery:
        locator:
          enabled: true                         # 启用服务发现路由
          lower-case-service-id: true          # 服务ID转换为小写
      
      # ===================================================================
      # 全局过滤器配置 - 应用于所有路由的过滤器
      # ===================================================================
      default-filters:
        # 重试过滤器 - 自动重试失败的请求
        - name: Retry
          args:
            retries: 3                          # 最大重试次数
            methods: GET,POST,PUT,DELETE        # 支持重试的HTTP方法
            series: SERVER_ERROR                # 重试的错误类型（5xx错误）
            exceptions: java.io.IOException,java.util.concurrent.TimeoutException
        # 请求时间过滤器 - 记录请求处理时间
        - name: RequestTime
          args:
            enabled: true                       # 启用请求时间记录
        # 添加请求头过滤器 - 为所有请求添加网关版本信息
        - name: AddRequestHeader
          args:
            name: X-Gateway-Version             # 请求头名称
            value: 1.0.0                        # 网关版本号
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 网关核心配置
wit:
  gateway:
    # 基础配置
    basic:
      # 服务名称
      service-name: wit-gateway
      # 服务版本
      version: 1.0.0
      # 服务描述
      description: Wit Mall API Gateway Service
      
    # 监控配置
    monitoring:
      enabled: true
      slow-request-threshold: 3000
      metrics-retention-hours: 24
      alert-enabled: true
      
    # 健康检查配置
    health-check:
      enabled: true
      check-interval: 30
      timeout: 5000
      
    # 请求配置
    request:
      # 请求大小限制
      max-request-size: 10MB
      # 请求头限制
      max-header-size: 8KB
      # 请求超时时间
      timeout: 30000
