server:
  port: 8082

spring:
  application:
    name: wit-user
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: wit-mall
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: wit-mall
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: wit-jwt-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: mysql-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: redis-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: rabbitmq-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: dubbo-config.yml
            group: DEFAULT_GROUP
            refresh: true

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 2000

# MyBatis Plus 配置已移至 Nacos 公共配置 (common-database.yml)

# Redis 配置
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 1
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# RabbitMQ 配置
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    connection-timeout: 15000
    publisher-confirms: true
    publisher-returns: true
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 5
        max-concurrency: 10
        retry:
          enabled: true
          max-attempts: 3

# Dubbo 配置
dubbo:
  application:
    name: ${spring.application.name}
  registry:
    address: nacos://localhost:8848
    parameters:
      namespace: wit-mall
  protocol:
    name: dubbo
    port: 20882
  scan:
    base-packages: com.wit.user.service.impl

# Seata 配置
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: wit-mall-group
  registry:
    type: nacos
    nacos:
      server-addr: localhost:8848
      namespace: wit-mall
      group: SEATA_GROUP
  config:
    type: nacos
    nacos:
      server-addr: localhost:8848
      namespace: wit-mall
      group: SEATA_GROUP

# Sentinel 配置
spring:
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8080
        port: 8719
      datasource:
        ds1:
          nacos:
            server-addr: localhost:8848
            dataId: ${spring.application.name}-sentinel
            groupId: DEFAULT_GROUP
            rule-type: flow

# 日志配置
logging:
  level:
    com.wit.user: debug
