# Spring Boot 3 现代化配置 - Common服务配置模板
# 使用 Config Data API 和 Nacos 配置中心
# 这是学习Spring Boot 3最新特性的最佳实践

# 基本日志配置（详细配置在 Nacos 中）
logging:
  level:
    root: INFO
    com.wit: DEBUG
    org.springframework.web: INFO
    org.springframework.security: WARN
    org.mybatis: INFO
    # 关闭不必要的DEBUG日志
    org.springframework.core.env: WARN
    org.springframework.boot.autoconfigure: WARN

# Spring Boot 3 Config Data API 配置
spring:
  application:
    name: wit-common
  profiles:
    active: prod

  # 禁用数据源自动配置（Common模块不需要独立数据库连接）
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

  cloud:
    # Nacos配置 - Spring Boot 3 现代化方式
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:***************:8848}
        namespace: ${NACOS_NAMESPACE:1f290fcd-60d0-48d1-893c-0d2ffb30625d}
        group: COMMON_GROUP
        cluster-name: wit-cluster
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        metadata:
          version: 1.0.0
          zone: prod
          service-type: common
      config:
        server-addr: ${NACOS_SERVER_ADDR:***************:8848}
        namespace: ${NACOS_NAMESPACE:1f290fcd-60d0-48d1-893c-0d2ffb30625d}
        group: COMMON_GROUP
        file-extension: yml
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        refresh-enabled: true
        # 强制指定服务器地址，避免使用localhost
        remote-first: true
        # 共享配置
        shared-configs:
          - data-id: common-database.yml
            group: COMMON_GROUP
            refresh: true
          - data-id: common-redis.yml
            group: COMMON_GROUP
            refresh: true
          - data-id: common-logging.yml
            group: COMMON_GROUP
            refresh: true
          - data-id: wit-jwt-config.yml
            group: DEFAULT_GROUP
            refresh: true

  # 允许Bean定义覆盖（解决配置冲突）
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

# 服务器配置（基础配置，详细配置在Nacos中）
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true

# Common模块特定配置
wit:
  common:
    # 是否启用Common模块功能
    enabled: true
    # 版本信息
    version: 1.0.0
    # 描述
    description: "WitMall公共模块 - 提供统一的工具类、配置和基础组件"
