package com.wit.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * JWT Token中的用户信息
 * 
 * 用于在JWT Token中存储和传递用户信息
 * 包含电商系统所需的完整用户信息
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UserTokenInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户角色列表
     */
    private List<String> roles;

    /**
     * 用户权限列表
     */
    private List<String> permissions;

    /**
     * 用户类型
     * 1-普通用户，2-商家，3-代理商，4-管理员
     */
    private String userType;

    /**
     * 用户等级
     * 如：青铜会员、白银会员、黄金会员、钻石会员
     */
    private String userLevel;

    /**
     * VIP等级
     * 如：VIP1、VIP2、VIP3等
     */
    private String vipLevel;

    /**
     * 是否为VIP用户
     */
    private Boolean isVip;

    /**
     * 用户积分
     */
    private Integer points;

    /**
     * 用户余额
     */
    private String balance;

    /**
     * 用户状态
     * 1-正常，0-禁用，-1-删除
     */
    private Integer status;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 设备类型
     * WEB、MOBILE、APP等
     */
    private String deviceType;

    // ==================== 便捷方法 ====================

    /**
     * 判断是否为管理员
     */
    public boolean isAdmin() {
        return roles != null && (roles.contains("ADMIN") || roles.contains("SUPER_ADMIN"));
    }

    /**
     * 判断是否为商家
     */
    public boolean isMerchant() {
        return "2".equals(userType) || (roles != null && roles.contains("MERCHANT"));
    }

    /**
     * 判断是否为代理商
     */
    public boolean isAgent() {
        return "3".equals(userType) || (roles != null && roles.contains("AGENT"));
    }

    /**
     * 判断是否有指定角色
     */
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }

    /**
     * 判断是否有任意一个指定角色
     */
    public boolean hasAnyRole(String... roles) {
        if (this.roles == null || this.roles.isEmpty()) {
            return false;
        }
        for (String role : roles) {
            if (this.roles.contains(role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否有指定权限
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }

    /**
     * 判断是否有任意一个指定权限
     */
    public boolean hasAnyPermission(String... permissions) {
        if (this.permissions == null || this.permissions.isEmpty()) {
            return false;
        }
        for (String permission : permissions) {
            if (this.permissions.contains(permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断用户是否正常状态
     */
    public boolean isActive() {
        return Integer.valueOf(1).equals(status);
    }

    /**
     * 判断用户是否被禁用
     */
    public boolean isDisabled() {
        return Integer.valueOf(0).equals(status);
    }

    /**
     * 判断用户是否被删除
     */
    public boolean isDeleted() {
        return Integer.valueOf(-1).equals(status);
    }

    /**
     * 获取显示名称（优先昵称，其次用户名）
     */
    public String getDisplayName() {
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname;
        }
        return username != null ? username : "未知用户";
    }

    /**
     * 获取脱敏手机号
     */
    public String getMaskedPhone() {
        if (phone == null || phone.length() < 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 获取脱敏邮箱
     */
    public String getMaskedEmail() {
        if (email == null || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        if (parts[0].length() <= 2) {
            return email;
        }
        return parts[0].substring(0, 2) + "***@" + parts[1];
    }

    /**
     * 转换为简单的用户信息（用于日志记录等）
     */
    public String toSimpleString() {
        return String.format("User{id=%s, username=%s, type=%s, status=%s}", 
                userId, username, userType, status);
    }

    /**
     * 验证用户信息是否完整
     */
    public boolean isValid() {
        return userId != null && !userId.trim().isEmpty() 
                && username != null && !username.trim().isEmpty()
                && status != null;
    }

    /**
     * 创建Builder
     */
    public static UserTokenInfoBuilder builder() {
        return new UserTokenInfoBuilder();
    }

    /**
     * Builder模式
     */
    public static class UserTokenInfoBuilder {
        private UserTokenInfo userTokenInfo = new UserTokenInfo();

        public UserTokenInfoBuilder userId(String userId) {
            userTokenInfo.setUserId(userId);
            return this;
        }

        public UserTokenInfoBuilder username(String username) {
            userTokenInfo.setUsername(username);
            return this;
        }

        public UserTokenInfoBuilder nickname(String nickname) {
            userTokenInfo.setNickname(nickname);
            return this;
        }

        public UserTokenInfoBuilder email(String email) {
            userTokenInfo.setEmail(email);
            return this;
        }

        public UserTokenInfoBuilder phone(String phone) {
            userTokenInfo.setPhone(phone);
            return this;
        }

        public UserTokenInfoBuilder avatar(String avatar) {
            userTokenInfo.setAvatar(avatar);
            return this;
        }

        public UserTokenInfoBuilder tenantId(String tenantId) {
            userTokenInfo.setTenantId(tenantId);
            return this;
        }

        public UserTokenInfoBuilder roles(List<String> roles) {
            userTokenInfo.setRoles(roles);
            return this;
        }

        public UserTokenInfoBuilder permissions(List<String> permissions) {
            userTokenInfo.setPermissions(permissions);
            return this;
        }

        public UserTokenInfoBuilder userType(String userType) {
            userTokenInfo.setUserType(userType);
            return this;
        }

        public UserTokenInfoBuilder userLevel(String userLevel) {
            userTokenInfo.setUserLevel(userLevel);
            return this;
        }

        public UserTokenInfoBuilder vipLevel(String vipLevel) {
            userTokenInfo.setVipLevel(vipLevel);
            return this;
        }

        public UserTokenInfoBuilder isVip(Boolean isVip) {
            userTokenInfo.setIsVip(isVip);
            return this;
        }

        public UserTokenInfoBuilder points(Integer points) {
            userTokenInfo.setPoints(points);
            return this;
        }

        public UserTokenInfoBuilder balance(String balance) {
            userTokenInfo.setBalance(balance);
            return this;
        }

        public UserTokenInfoBuilder status(Integer status) {
            userTokenInfo.setStatus(status);
            return this;
        }

        public UserTokenInfoBuilder loginTime(Date loginTime) {
            userTokenInfo.setLoginTime(loginTime);
            return this;
        }

        public UserTokenInfoBuilder loginIp(String loginIp) {
            userTokenInfo.setLoginIp(loginIp);
            return this;
        }

        public UserTokenInfoBuilder deviceType(String deviceType) {
            userTokenInfo.setDeviceType(deviceType);
            return this;
        }

        public UserTokenInfo build() {
            return userTokenInfo;
        }
    }
}
