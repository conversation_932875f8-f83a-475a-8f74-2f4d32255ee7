package com.wit.common.context;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 用户上下文工具类
 * 用于获取当前请求的用户信息
 * 
 * <AUTHOR>
 */
@Slf4j
public class UserContext {

    private static final String USER_ID_HEADER = "X-User-Id";
    private static final String USERNAME_HEADER = "X-Username";
    private static final String TENANT_ID_HEADER = "X-Tenant-Id";
    private static final String USER_ROLES_HEADER = "X-User-Roles";

    /**
     * 获取当前用户ID
     */
    public static String getCurrentUserId() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                // 优先从请求属性获取
                String userId = (String) request.getAttribute("userId");
                if (userId != null) {
                    return userId;
                }
                // 从请求头获取
                return request.getHeader(USER_ID_HEADER);
            }
        } catch (Exception e) {
            log.warn("获取当前用户ID失败", e);
        }
        return null;
    }

    /**
     * 获取当前用户名
     */
    public static String getCurrentUsername() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                // 优先从请求属性获取
                String username = (String) request.getAttribute("username");
                if (username != null) {
                    return username;
                }
                // 从请求头获取
                return request.getHeader(USERNAME_HEADER);
            }
        } catch (Exception e) {
            log.warn("获取当前用户名失败", e);
        }
        return null;
    }

    /**
     * 获取当前租户ID
     */
    public static String getCurrentTenantId() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                // 优先从请求属性获取
                String tenantId = (String) request.getAttribute("tenantId");
                if (tenantId != null) {
                    return tenantId;
                }
                // 从请求头获取
                return request.getHeader(TENANT_ID_HEADER);
            }
        } catch (Exception e) {
            log.warn("获取当前租户ID失败", e);
        }
        return null;
    }

    /**
     * 获取当前用户角色
     */
    @SuppressWarnings("unchecked")
    public static List<String> getCurrentUserRoles() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                // 优先从请求属性获取
                List<String> roles = (List<String>) request.getAttribute("roles");
                if (roles != null) {
                    return roles;
                }
                // 从请求头获取
                String rolesHeader = request.getHeader(USER_ROLES_HEADER);
                if (rolesHeader != null && !rolesHeader.isEmpty()) {
                    return Arrays.asList(rolesHeader.split(","));
                }
            }
        } catch (Exception e) {
            log.warn("获取当前用户角色失败", e);
        }
        return null;
    }

    /**
     * 获取当前用户信息
     */
    public static UserInfo getCurrentUser() {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(getCurrentUserId());
        userInfo.setUsername(getCurrentUsername());
        userInfo.setTenantId(getCurrentTenantId());
        userInfo.setRoles(getCurrentUserRoles());
        return userInfo;
    }

    /**
     * 检查当前用户是否有指定角色
     */
    public static boolean hasRole(String role) {
        List<String> roles = getCurrentUserRoles();
        return roles != null && roles.contains(role);
    }

    /**
     * 检查当前用户是否有任意一个指定角色
     */
    public static boolean hasAnyRole(String... roles) {
        List<String> userRoles = getCurrentUserRoles();
        if (userRoles == null || userRoles.isEmpty()) {
            return false;
        }
        for (String role : roles) {
            if (userRoles.contains(role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前用户是否为管理员
     */
    public static boolean isAdmin() {
        return hasAnyRole("ADMIN", "SUPER_ADMIN");
    }

    /**
     * 获取当前请求
     */
    private static HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            log.debug("无法获取当前请求上下文", e);
            return null;
        }
    }

    /**
     * 用户信息类
     */
    @Data
    public static class UserInfo {
        private String userId;
        private String username;
        private String tenantId;
        private List<String> roles;

        public boolean isValid() {
            return userId != null && !userId.isEmpty();
        }
    }
}
