package com.wit.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wit.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户基础信息实体（共享实体）
 * 
 * 用途：
 * 1. 在订单服务中记录下单用户信息
 * 2. 在购物车服务中关联用户信息
 * 3. 在支付服务中记录支付用户信息
 * 4. 在评价服务中显示评价用户信息
 * 5. 在营销服务中进行用户画像分析
 * 
 * 注意：这是一个轻量级的用户信息实体，只包含最基本的用户信息
 * 完整的用户信息请使用用户服务中的User实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("user_basic_info")
public class UserBasicInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID（关联用户服务的用户ID）
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 头像URL
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 手机号（脱敏）
     */
    @TableField("phone")
    private String phone;

    /**
     * 邮箱（脱敏）
     */
    @TableField("email")
    private String email;

    /**
     * 用户类型
     * 1-普通用户，2-商家，3-代理商，4-管理员
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 用户等级
     */
    @TableField("user_level")
    private String userLevel;

    /**
     * VIP等级
     */
    @TableField("vip_level")
    private String vipLevel;

    /**
     * 是否为VIP用户
     */
    @TableField("is_vip")
    private Boolean isVip;

    /**
     * 用户状态
     * 1-正常，0-禁用，-1-删除
     */
    @TableField("status")
    private Integer status;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    // ==================== 便捷方法 ====================

    /**
     * 获取显示名称（优先昵称，其次用户名）
     */
    public String getDisplayName() {
        if (nickname != null && !nickname.trim().isEmpty()) {
            return nickname;
        }
        return username != null ? username : "未知用户";
    }

    /**
     * 判断是否为VIP用户
     */
    public boolean isVipUser() {
        return Boolean.TRUE.equals(isVip);
    }

    /**
     * 判断是否为商家
     */
    public boolean isMerchant() {
        return Integer.valueOf(2).equals(userType);
    }

    /**
     * 判断是否为代理商
     */
    public boolean isAgent() {
        return Integer.valueOf(3).equals(userType);
    }

    /**
     * 判断是否为管理员
     */
    public boolean isAdmin() {
        return Integer.valueOf(4).equals(userType);
    }

    /**
     * 判断用户是否正常状态
     */
    public boolean isActive() {
        return Integer.valueOf(1).equals(status);
    }

    /**
     * 获取用户类型描述
     */
    public String getUserTypeDesc() {
        if (userType == null) {
            return "未知";
        }
        switch (userType) {
            case 1:
                return "普通用户";
            case 2:
                return "商家";
            case 3:
                return "代理商";
            case 4:
                return "管理员";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "正常";
            case 0:
                return "禁用";
            case -1:
                return "删除";
            default:
                return "未知";
        }
    }

    /**
     * 转换为简单字符串（用于日志）
     */
    public String toSimpleString() {
        return String.format("UserBasicInfo{userId=%d, username=%s, nickname=%s, userType=%d, status=%d}", 
                userId, username, nickname, userType, status);
    }

    /**
     * 从完整用户信息创建基础用户信息
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param nickname 昵称
     * @param avatar 头像
     * @param phone 手机号
     * @param email 邮箱
     * @param userType 用户类型
     * @param userLevel 用户等级
     * @param vipLevel VIP等级
     * @param isVip 是否VIP
     * @param status 状态
     * @param tenantId 租户ID
     * @return 用户基础信息
     */
    public static UserBasicInfo create(Long userId, String username, String nickname, String avatar,
                                     String phone, String email, Integer userType, String userLevel,
                                     String vipLevel, Boolean isVip, Integer status, String tenantId) {
        UserBasicInfo userBasicInfo = new UserBasicInfo();
        userBasicInfo.setUserId(userId);
        userBasicInfo.setUsername(username);
        userBasicInfo.setNickname(nickname);
        userBasicInfo.setAvatar(avatar);
        userBasicInfo.setPhone(maskPhone(phone));
        userBasicInfo.setEmail(maskEmail(email));
        userBasicInfo.setUserType(userType);
        userBasicInfo.setUserLevel(userLevel);
        userBasicInfo.setVipLevel(vipLevel);
        userBasicInfo.setIsVip(isVip);
        userBasicInfo.setStatus(status);
        userBasicInfo.setTenantId(tenantId);
        return userBasicInfo;
    }

    /**
     * 手机号脱敏
     */
    private static String maskPhone(String phone) {
        if (phone == null || phone.length() < 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 邮箱脱敏
     */
    private static String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        if (parts[0].length() <= 2) {
            return email;
        }
        return parts[0].substring(0, 2) + "***@" + parts[1];
    }

    /**
     * 创建Builder
     */
    public static UserBasicInfoBuilder builder() {
        return new UserBasicInfoBuilder();
    }

    /**
     * Builder模式
     */
    public static class UserBasicInfoBuilder {
        private UserBasicInfo userBasicInfo = new UserBasicInfo();

        public UserBasicInfoBuilder userId(Long userId) {
            userBasicInfo.setUserId(userId);
            return this;
        }

        public UserBasicInfoBuilder username(String username) {
            userBasicInfo.setUsername(username);
            return this;
        }

        public UserBasicInfoBuilder nickname(String nickname) {
            userBasicInfo.setNickname(nickname);
            return this;
        }

        public UserBasicInfoBuilder avatar(String avatar) {
            userBasicInfo.setAvatar(avatar);
            return this;
        }

        public UserBasicInfoBuilder phone(String phone) {
            userBasicInfo.setPhone(phone);
            return this;
        }

        public UserBasicInfoBuilder email(String email) {
            userBasicInfo.setEmail(email);
            return this;
        }

        public UserBasicInfoBuilder userType(Integer userType) {
            userBasicInfo.setUserType(userType);
            return this;
        }

        public UserBasicInfoBuilder userLevel(String userLevel) {
            userBasicInfo.setUserLevel(userLevel);
            return this;
        }

        public UserBasicInfoBuilder vipLevel(String vipLevel) {
            userBasicInfo.setVipLevel(vipLevel);
            return this;
        }

        public UserBasicInfoBuilder isVip(Boolean isVip) {
            userBasicInfo.setIsVip(isVip);
            return this;
        }

        public UserBasicInfoBuilder status(Integer status) {
            userBasicInfo.setStatus(status);
            return this;
        }

        public UserBasicInfoBuilder tenantId(String tenantId) {
            userBasicInfo.setTenantId(tenantId);
            return this;
        }

        public UserBasicInfo build() {
            return userBasicInfo;
        }
    }
}
