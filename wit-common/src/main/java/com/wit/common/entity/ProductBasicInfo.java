package com.wit.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wit.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品基础信息实体（共享实体）
 * 
 * 用途：
 * 1. 在订单服务中记录订单商品信息
 * 2. 在购物车服务中存储商品基本信息
 * 3. 在支付服务中记录支付商品信息
 * 4. 在库存服务中关联商品信息
 * 5. 在营销服务中进行商品推荐
 * 6. 在搜索服务中建立商品索引
 * 
 * 注意：这是一个轻量级的商品信息实体，只包含最基本的商品信息
 * 完整的商品信息请使用商品服务中的Product实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("product_basic_info")
public class ProductBasicInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品ID（关联商品服务的商品ID）
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品编码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 商品副标题
     */
    @TableField("product_subtitle")
    private String productSubtitle;

    /**
     * 商品主图
     */
    @TableField("main_image")
    private String mainImage;

    /**
     * 商品分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 商品分类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 商品品牌ID
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 商品品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 商品价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 商品原价
     */
    @TableField("original_price")
    private BigDecimal originalPrice;

    /**
     * 商品成本价
     */
    @TableField("cost_price")
    private BigDecimal costPrice;

    /**
     * 商品重量（克）
     */
    @TableField("weight")
    private Integer weight;

    /**
     * 商品单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 商品状态
     * 1-上架，0-下架，-1-删除
     */
    @TableField("status")
    private Integer status;

    /**
     * 商品库存
     */
    @TableField("stock")
    private Integer stock;

    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 商家名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    // ==================== 便捷方法 ====================

    /**
     * 判断商品是否上架
     */
    public boolean isOnSale() {
        return Integer.valueOf(1).equals(status);
    }

    /**
     * 判断商品是否下架
     */
    public boolean isOffSale() {
        return Integer.valueOf(0).equals(status);
    }

    /**
     * 判断商品是否已删除
     */
    public boolean isDeleted() {
        return Integer.valueOf(-1).equals(status);
    }

    /**
     * 判断商品是否有库存
     */
    public boolean hasStock() {
        return stock != null && stock > 0;
    }

    /**
     * 判断商品是否缺货
     */
    public boolean isOutOfStock() {
        return stock == null || stock <= 0;
    }

    /**
     * 获取商品状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "上架";
            case 0:
                return "下架";
            case -1:
                return "删除";
            default:
                return "未知";
        }
    }

    /**
     * 获取商品显示名称（包含品牌）
     */
    public String getDisplayName() {
        if (brandName != null && !brandName.trim().isEmpty()) {
            return brandName + " " + productName;
        }
        return productName;
    }

    /**
     * 获取商品简短描述
     */
    public String getShortDesc() {
        if (productSubtitle != null && !productSubtitle.trim().isEmpty()) {
            return productSubtitle.length() > 50 ? 
                   productSubtitle.substring(0, 50) + "..." : productSubtitle;
        }
        return productName;
    }

    /**
     * 计算折扣率
     */
    public BigDecimal getDiscountRate() {
        if (originalPrice == null || price == null || 
            originalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal discount = originalPrice.subtract(price);
        return discount.divide(originalPrice, 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 判断是否有折扣
     */
    public boolean hasDiscount() {
        return originalPrice != null && price != null && 
               originalPrice.compareTo(price) > 0;
    }

    /**
     * 获取节省金额
     */
    public BigDecimal getSavedAmount() {
        if (hasDiscount()) {
            return originalPrice.subtract(price);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 转换为简单字符串（用于日志）
     */
    public String toSimpleString() {
        return String.format("ProductBasicInfo{productId=%d, productCode=%s, productName=%s, price=%s, status=%d}", 
                productId, productCode, productName, price, status);
    }

    /**
     * 从完整商品信息创建基础商品信息
     */
    public static ProductBasicInfo create(Long productId, String productCode, String productName,
                                        String productSubtitle, String mainImage, Long categoryId,
                                        String categoryName, Long brandId, String brandName,
                                        BigDecimal price, BigDecimal originalPrice, BigDecimal costPrice,
                                        Integer weight, String unit, Integer status, Integer stock,
                                        Long merchantId, String merchantName, String tenantId) {
        ProductBasicInfo productBasicInfo = new ProductBasicInfo();
        productBasicInfo.setProductId(productId);
        productBasicInfo.setProductCode(productCode);
        productBasicInfo.setProductName(productName);
        productBasicInfo.setProductSubtitle(productSubtitle);
        productBasicInfo.setMainImage(mainImage);
        productBasicInfo.setCategoryId(categoryId);
        productBasicInfo.setCategoryName(categoryName);
        productBasicInfo.setBrandId(brandId);
        productBasicInfo.setBrandName(brandName);
        productBasicInfo.setPrice(price);
        productBasicInfo.setOriginalPrice(originalPrice);
        productBasicInfo.setCostPrice(costPrice);
        productBasicInfo.setWeight(weight);
        productBasicInfo.setUnit(unit);
        productBasicInfo.setStatus(status);
        productBasicInfo.setStock(stock);
        productBasicInfo.setMerchantId(merchantId);
        productBasicInfo.setMerchantName(merchantName);
        productBasicInfo.setTenantId(tenantId);
        return productBasicInfo;
    }

    /**
     * 创建Builder
     */
    public static ProductBasicInfoBuilder builder() {
        return new ProductBasicInfoBuilder();
    }

    /**
     * Builder模式
     */
    public static class ProductBasicInfoBuilder {
        private ProductBasicInfo productBasicInfo = new ProductBasicInfo();

        public ProductBasicInfoBuilder productId(Long productId) {
            productBasicInfo.setProductId(productId);
            return this;
        }

        public ProductBasicInfoBuilder productCode(String productCode) {
            productBasicInfo.setProductCode(productCode);
            return this;
        }

        public ProductBasicInfoBuilder productName(String productName) {
            productBasicInfo.setProductName(productName);
            return this;
        }

        public ProductBasicInfoBuilder productSubtitle(String productSubtitle) {
            productBasicInfo.setProductSubtitle(productSubtitle);
            return this;
        }

        public ProductBasicInfoBuilder mainImage(String mainImage) {
            productBasicInfo.setMainImage(mainImage);
            return this;
        }

        public ProductBasicInfoBuilder categoryId(Long categoryId) {
            productBasicInfo.setCategoryId(categoryId);
            return this;
        }

        public ProductBasicInfoBuilder categoryName(String categoryName) {
            productBasicInfo.setCategoryName(categoryName);
            return this;
        }

        public ProductBasicInfoBuilder brandId(Long brandId) {
            productBasicInfo.setBrandId(brandId);
            return this;
        }

        public ProductBasicInfoBuilder brandName(String brandName) {
            productBasicInfo.setBrandName(brandName);
            return this;
        }

        public ProductBasicInfoBuilder price(BigDecimal price) {
            productBasicInfo.setPrice(price);
            return this;
        }

        public ProductBasicInfoBuilder originalPrice(BigDecimal originalPrice) {
            productBasicInfo.setOriginalPrice(originalPrice);
            return this;
        }

        public ProductBasicInfoBuilder costPrice(BigDecimal costPrice) {
            productBasicInfo.setCostPrice(costPrice);
            return this;
        }

        public ProductBasicInfoBuilder weight(Integer weight) {
            productBasicInfo.setWeight(weight);
            return this;
        }

        public ProductBasicInfoBuilder unit(String unit) {
            productBasicInfo.setUnit(unit);
            return this;
        }

        public ProductBasicInfoBuilder status(Integer status) {
            productBasicInfo.setStatus(status);
            return this;
        }

        public ProductBasicInfoBuilder stock(Integer stock) {
            productBasicInfo.setStock(stock);
            return this;
        }

        public ProductBasicInfoBuilder merchantId(Long merchantId) {
            productBasicInfo.setMerchantId(merchantId);
            return this;
        }

        public ProductBasicInfoBuilder merchantName(String merchantName) {
            productBasicInfo.setMerchantName(merchantName);
            return this;
        }

        public ProductBasicInfoBuilder tenantId(String tenantId) {
            productBasicInfo.setTenantId(tenantId);
            return this;
        }

        public ProductBasicInfo build() {
            return productBasicInfo;
        }
    }
}
