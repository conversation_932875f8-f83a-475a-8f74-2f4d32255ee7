package com.wit.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回状态码枚举
 *
 * 状态码设计规范：
 * - 200-299: 成功状态码
 * - 400-499: 客户端错误状态码
 * - 500-599: 服务器错误状态码
 * - 1000-1999: 用户相关业务错误
 * - 2000-2999: 商品相关业务错误
 * - 3000-3999: 订单相关业务错误
 * - 4000-4999: 支付相关业务错误
 * - 5000-5999: 购物车相关业务错误
 * - 6000-6999: 库存相关业务错误
 * - 7000-7999: 文件相关业务错误
 * - 8000-8999: 营销相关业务错误
 * - 9000-9999: 系统相关业务错误
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // ==================== HTTP标准状态码 ====================
    SUCCESS(200, "操作成功"),
    CREATED(201, "创建成功"),
    ACCEPTED(202, "请求已接受"),
    NO_CONTENT(204, "无内容"),

    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    NOT_ACCEPTABLE(406, "不可接受的请求"),
    REQUEST_TIMEOUT(408, "请求超时"),
    CONFLICT(409, "资源冲突"),
    GONE(410, "资源已删除"),
    PRECONDITION_FAILED(412, "前置条件失败"),
    PAYLOAD_TOO_LARGE(413, "请求体过大"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的媒体类型"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误
    ERROR(500, "操作失败"),
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    NOT_IMPLEMENTED(501, "功能未实现"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // ==================== 用户相关业务错误 (1000-1999) ====================
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USERNAME_ALREADY_EXISTS(1003, "用户名已存在"),
    EMAIL_ALREADY_EXISTS(1004, "邮箱已存在"),
    PHONE_ALREADY_EXISTS(1005, "手机号已存在"),
    PASSWORD_ERROR(1006, "密码错误"),
    OLD_PASSWORD_ERROR(1007, "原密码错误"),
    PASSWORD_TOO_WEAK(1008, "密码强度不够"),
    PASSWORD_SAME_AS_OLD(1009, "新密码不能与原密码相同"),
    USER_DISABLED(1010, "用户已被禁用"),
    USER_LOCKED(1011, "用户已被锁定"),
    USER_NOT_ACTIVATED(1012, "用户未激活"),
    TOKEN_EXPIRED(1013, "Token已过期"),
    TOKEN_INVALID(1014, "Token无效"),
    TOKEN_MISSING(1015, "缺少Token"),
    REFRESH_TOKEN_EXPIRED(1016, "刷新Token已过期"),
    REFRESH_TOKEN_INVALID(1017, "刷新Token无效"),
    LOGIN_FAILED(1018, "登录失败"),
    LOGOUT_FAILED(1019, "退出登录失败"),
    REGISTER_FAILED(1020, "注册失败"),
    CAPTCHA_ERROR(1021, "验证码错误"),
    CAPTCHA_EXPIRED(1022, "验证码已过期"),
    SMS_CODE_ERROR(1023, "短信验证码错误"),
    SMS_CODE_EXPIRED(1024, "短信验证码已过期"),
    EMAIL_CODE_ERROR(1025, "邮箱验证码错误"),
    EMAIL_CODE_EXPIRED(1026, "邮箱验证码已过期"),
    PERMISSION_DENIED(1027, "权限不足"),
    ROLE_NOT_FOUND(1028, "角色不存在"),
    PERMISSION_NOT_FOUND(1029, "权限不存在"),

    // ==================== 商品相关业务错误 (2000-2999) ====================
    PRODUCT_NOT_FOUND(2001, "商品不存在"),
    PRODUCT_STOCK_INSUFFICIENT(2002, "商品库存不足"),
    PRODUCT_OFFLINE(2003, "商品已下架"),
    PRODUCT_DELETED(2004, "商品已删除"),
    PRODUCT_CATEGORY_NOT_FOUND(2005, "商品分类不存在"),
    PRODUCT_BRAND_NOT_FOUND(2006, "商品品牌不存在"),
    PRODUCT_SKU_NOT_FOUND(2007, "商品SKU不存在"),
    PRODUCT_PRICE_ERROR(2008, "商品价格错误"),
    PRODUCT_NAME_DUPLICATE(2009, "商品名称重复"),
    PRODUCT_CODE_DUPLICATE(2010, "商品编码重复"),

    // ==================== 订单相关业务错误 (3000-3999) ====================
    ORDER_NOT_FOUND(3001, "订单不存在"),
    ORDER_STATUS_ERROR(3002, "订单状态错误"),
    ORDER_CANNOT_CANCEL(3003, "订单无法取消"),
    ORDER_CANNOT_PAY(3004, "订单无法支付"),
    ORDER_CANNOT_REFUND(3005, "订单无法退款"),
    ORDER_ALREADY_PAID(3006, "订单已支付"),
    ORDER_ALREADY_CANCELLED(3007, "订单已取消"),
    ORDER_ALREADY_COMPLETED(3008, "订单已完成"),
    ORDER_EXPIRED(3009, "订单已过期"),
    ORDER_ITEM_NOT_FOUND(3010, "订单商品不存在"),
    ORDER_AMOUNT_ERROR(3011, "订单金额错误"),
    ORDER_CREATE_FAILED(3012, "订单创建失败"),

    // ==================== 支付相关业务错误 (4000-4999) ====================
    PAYMENT_FAILED(4001, "支付失败"),
    PAYMENT_TIMEOUT(4002, "支付超时"),
    PAYMENT_CANCELLED(4003, "支付已取消"),
    PAYMENT_METHOD_NOT_SUPPORTED(4004, "不支持的支付方式"),
    PAYMENT_AMOUNT_ERROR(4005, "支付金额错误"),
    PAYMENT_ALREADY_EXISTS(4006, "支付记录已存在"),
    REFUND_FAILED(4007, "退款失败"),
    REFUND_AMOUNT_ERROR(4008, "退款金额错误"),
    BALANCE_INSUFFICIENT(4009, "余额不足"),

    // ==================== 购物车相关业务错误 (5000-5999) ====================
    CART_ITEM_NOT_FOUND(5001, "购物车商品不存在"),
    CART_ITEM_ALREADY_EXISTS(5002, "购物车商品已存在"),
    CART_EMPTY(5003, "购物车为空"),
    CART_ITEM_QUANTITY_ERROR(5004, "购物车商品数量错误"),
    CART_ITEM_LIMIT_EXCEEDED(5005, "购物车商品数量超限"),

    // ==================== 库存相关业务错误 (6000-6999) ====================
    INVENTORY_LOCK_FAILED(6001, "库存锁定失败"),
    INVENTORY_UNLOCK_FAILED(6002, "库存解锁失败"),
    INVENTORY_INSUFFICIENT(6003, "库存不足"),
    INVENTORY_NOT_FOUND(6004, "库存记录不存在"),
    INVENTORY_OPERATION_FAILED(6005, "库存操作失败"),

    // ==================== 文件相关业务错误 (7000-7999) ====================
    FILE_UPLOAD_FAILED(7001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(7002, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(7003, "文件大小超限"),
    FILE_NOT_FOUND(7004, "文件不存在"),
    FILE_DELETE_FAILED(7005, "文件删除失败"),
    FILE_DOWNLOAD_FAILED(7006, "文件下载失败"),
    IMAGE_PROCESS_FAILED(7007, "图片处理失败"),

    // ==================== 营销相关业务错误 (8000-8999) ====================
    COUPON_NOT_FOUND(8001, "优惠券不存在"),
    COUPON_EXPIRED(8002, "优惠券已过期"),
    COUPON_USED(8003, "优惠券已使用"),
    COUPON_NOT_AVAILABLE(8004, "优惠券不可用"),
    PROMOTION_NOT_FOUND(8005, "促销活动不存在"),
    PROMOTION_EXPIRED(8006, "促销活动已过期"),
    PROMOTION_NOT_STARTED(8007, "促销活动未开始"),

    // ==================== 系统相关业务错误 (9000-9999) ====================
    SYSTEM_BUSY(9001, "系统繁忙，请稍后重试"),
    SYSTEM_MAINTENANCE(9002, "系统维护中"),
    SYSTEM_ERROR(9003, "系统错误"),
    DATABASE_ERROR(9004, "数据库错误"),
    CACHE_ERROR(9005, "缓存错误"),
    MQ_ERROR(9006, "消息队列错误"),
    THIRD_PARTY_ERROR(9007, "第三方服务错误"),
    CONFIG_ERROR(9008, "配置错误"),
    NETWORK_ERROR(9009, "网络错误"),
    DATA_INTEGRITY_ERROR(9010, "数据完整性错误");

    private final Integer code;
    private final String message;

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return ERROR;
    }

    /**
     * 判断是否为成功状态码
     */
    public boolean isSuccess() {
        return SUCCESS.equals(this) || CREATED.equals(this) || ACCEPTED.equals(this) || NO_CONTENT.equals(this);
    }

    /**
     * 判断是否为客户端错误
     */
    public boolean isClientError() {
        return code >= 400 && code < 500;
    }

    /**
     * 判断是否为服务器错误
     */
    public boolean isServerError() {
        return code >= 500 && code < 600;
    }

    /**
     * 判断是否为业务错误
     */
    public boolean isBusinessError() {
        return code >= 1000 && code < 10000;
    }
}
