package com.wit.common.result;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 统一返回结果
 *
 * 功能特性：
 * 1. 统一的响应格式，包含状态码、消息、数据和时间戳
 * 2. 支持泛型，可以返回任意类型的数据
 * 3. 提供丰富的静态方法，方便创建不同类型的响应
 * 4. 支持链式调用，提高代码可读性
 * 5. 自动添加时间戳，便于调试和日志记录
 * 6. 支持请求路径记录，便于问题定位
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 返回消息
     */
    private String message;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 格式化的时间字符串
     */
    private String time;

    /**
     * 请求路径（可选）
     */
    private String path;

    /**
     * 请求ID（可选，用于链路追踪）
     */
    private String requestId;

    /**
     * 服务名称（可选）
     */
    private String service;

    public Result() {
        this.timestamp = System.currentTimeMillis();
        this.time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public Result(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public Result(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    // ==================== 成功响应方法 ====================

    /**
     * 成功返回（无数据）
     */
    public static <T> Result<T> success() {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
    }

    /**
     * 成功返回（带数据）
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    /**
     * 成功返回（自定义消息和数据）
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message, data);
    }

    /**
     * 成功返回（自定义消息，无数据）
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(ResultCode.SUCCESS.getCode(), message);
    }

    // ==================== 失败响应方法 ====================

    /**
     * 失败返回（默认错误）
     */
    public static <T> Result<T> error() {
        return new Result<>(ResultCode.ERROR.getCode(), ResultCode.ERROR.getMessage());
    }

    /**
     * 失败返回（自定义消息）
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(ResultCode.ERROR.getCode(), message);
    }

    /**
     * 失败返回（自定义状态码和消息）
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message);
    }

    /**
     * 失败返回（使用枚举）
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return new Result<>(resultCode.getCode(), resultCode.getMessage());
    }

    /**
     * 失败返回（使用枚举和自定义消息）
     */
    public static <T> Result<T> error(ResultCode resultCode, String customMessage) {
        return new Result<>(resultCode.getCode(), customMessage);
    }

    // ==================== 特殊响应方法 ====================

    /**
     * 参数错误响应
     */
    public static <T> Result<T> paramError(String message) {
        return new Result<>(ResultCode.PARAM_ERROR.getCode(), message);
    }

    /**
     * 未授权响应
     */
    public static <T> Result<T> unauthorized() {
        return new Result<>(ResultCode.UNAUTHORIZED.getCode(), ResultCode.UNAUTHORIZED.getMessage());
    }

    /**
     * 未授权响应（自定义消息）
     */
    public static <T> Result<T> unauthorized(String message) {
        return new Result<>(ResultCode.UNAUTHORIZED.getCode(), message);
    }

    /**
     * 禁止访问响应
     */
    public static <T> Result<T> forbidden() {
        return new Result<>(ResultCode.FORBIDDEN.getCode(), ResultCode.FORBIDDEN.getMessage());
    }

    /**
     * 禁止访问响应（自定义消息）
     */
    public static <T> Result<T> forbidden(String message) {
        return new Result<>(ResultCode.FORBIDDEN.getCode(), message);
    }

    /**
     * 资源不存在响应
     */
    public static <T> Result<T> notFound() {
        return new Result<>(ResultCode.NOT_FOUND.getCode(), ResultCode.NOT_FOUND.getMessage());
    }

    /**
     * 资源不存在响应（自定义消息）
     */
    public static <T> Result<T> notFound(String message) {
        return new Result<>(ResultCode.NOT_FOUND.getCode(), message);
    }

    /**
     * 请求过于频繁响应
     */
    public static <T> Result<T> tooManyRequests() {
        return new Result<>(ResultCode.TOO_MANY_REQUESTS.getCode(), ResultCode.TOO_MANY_REQUESTS.getMessage());
    }

    /**
     * 系统繁忙响应
     */
    public static <T> Result<T> systemBusy() {
        return new Result<>(ResultCode.SYSTEM_BUSY.getCode(), ResultCode.SYSTEM_BUSY.getMessage());
    }

    // ==================== 链式调用方法 ====================

    /**
     * 设置请求路径
     */
    public Result<T> path(String path) {
        this.path = path;
        return this;
    }

    /**
     * 设置请求ID
     */
    public Result<T> requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    /**
     * 设置服务名称
     */
    public Result<T> service(String service) {
        this.service = service;
        return this;
    }

    // ==================== 判断方法 ====================

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }

    /**
     * 判断是否有数据
     */
    public boolean hasData() {
        return this.data != null;
    }

    /**
     * 获取数据，如果为空则返回默认值
     */
    public T getDataOrDefault(T defaultValue) {
        return this.data != null ? this.data : defaultValue;
    }
}
